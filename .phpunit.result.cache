{"version": 1, "defects": {"Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testRejectsTooLongQueryParams": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testPartialEdit": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testOriginTimeoutTooSmall": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testOriginUrlWithLocalhostIp": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSwitchStreamOriginToNonStreamOrigin": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testUpdatingToEmptyCnames": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testGroupChangeToLsGroup": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testResourceEditWithBucketOriginEnabled": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSwitchNonStreamOriginToStreamOrigin": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testDisablingOriginTimeout": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testResourceEditBucketFailWithInvalidType": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailedUnallowedCname": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testResourceEditBucketFailsWithInvalidHost": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testInvalidOriginPort": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailNotSupplyingOriginPort": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailedUnallowedOriginDomain": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testHttpsRedirectCodeChange": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testAcceptsNullInHttpsRedirectCodeAndDoesNothing": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testWorksOnUpdatingSelfCname": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testResourceEditWithBucketOriginDisabled": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testUnprocessableModification": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testChangingAccountCreatesAccount": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testChangingIgnoredQueryParams": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSettingIgnoredQueryParamsWhenNoExisted": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testResettingOriginPortWorks": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testResourceForEditNotFound": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testEditFailsWithBadDomain with data set #5": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testEditFailsWithBadDomain with data set #3": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testEditFailsWithBadDomain with data set #1": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testEditFailsWithBadDomain with data set #0": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testEditFailsWithBadDomain with data set #2": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testEditFailsWithBadDomain with data set #4": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testRejectsInvalidHttpsRedirectCode": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testOriginUrlWithReservedIp": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testForwardHostHeader": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailsWhenAddingExistingCnameWithDifferentCase": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testChangingIgnoredQueryParamsWithTooManyItems": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testRejectsInvalidQueryParamFormat": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testAcceptsZeroToResetHttpsRedirectCode": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testForwardHostHeaderIsNotMandatoryAndNotReset": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSettingIgnoredQueryParamsWithUpperLimit": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testChangingOriginTimeout": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testEditProtectionOnly": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testStreamingPlaylistBypass": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSettingOriginHeaders with data set \"set data 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSettingOriginHeaders with data set \"set empty 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSettingOriginHeaders with data set \"change\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSettingOriginHeaders with data set \"set empty 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSettingOriginHeaders with data set \"set empty 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSettingOriginHeaders with data set \"set data 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testChangingAccount": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailureOnDuplicateCname": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testOriginTimeoutTooLarge": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testCustomLocationIsPreservedWhenGroupIsUnchanged": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testPartialInput": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testRejectsDuplicateQueryParams": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testEdit": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testNoChangeIsDoneWhenIgnoredQueryParamsIsNotSet": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testWithCnameLong65Bytes": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testCustomLocationIsDeletedWhenGroupIsChanged": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 7\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 9\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 10\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 8\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 11\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 12\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testWithCnameLong64Bytes": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSettingCustomData": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testNullingCustomData": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailMissingParameters": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testChangingAccountToSameAccountDoesNothing": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testStreamingPlaylistBypassIsNotMandatoryAndNotReset": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\responseHeaders\\EditTest::testOk with data set \"some header -> empty\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\responseHeaders\\EditTest::testOk with data set \"some header -> another header\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\responseHeaders\\EditTest::testOk with data set \"empty -> empty\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\responseHeaders\\EditTest::testFail with data set \"Invalid name with colon\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\responseHeaders\\EditTest::testFail with data set \"Invalid value with backslash\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\responseHeaders\\EditTest::testFailWhenResponseHeadersMissing": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\EditTest::testPartialEdit": 1, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\ResourceEditSchemaTest::testFunctionality": 4, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testMultipleOriginsWithFailoverOriginPropertiesSameAsResource": 3, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testSecureToken with data set \"some set 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testSecureToken with data set \"all set 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testSecureToken with data set \"disabled\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testMultipleOrigins": 3, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testFollowRedirect with data set \"all set 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testFollowRedirect with data set \"all set 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testFollowRedirect with data set \"only enabled\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testFollowRedirect with data set \"disabled\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::test": 3, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testIgnoreQueryParams": 3, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourcesControllerTest::test": 3, "Cdn77\\NxgApi\\Tests\\Unit\\DTO\\NxgConfGen\\ResourceDTOTest::testInterfaceWorks with data set #1": 4, "Cdn77\\NxgApi\\Tests\\Unit\\DTO\\NxgConfGen\\ResourceDTOTest::testInterfaceWorks with data set #0": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testOk with data set \"empty 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testOk with data set \"data 1\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithIgnoredQueryParamsSet": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testAccountExceptionForUnallowedCnameDomain": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOriginTimeout": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithHttpsRedirectCodeSet": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testStreamingPlaylistBypassIsNotMandatory": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithIp": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOriginTimeoutWithZero": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testStreamingPlaylistBypassShouldBeEnabledForStreamOrigin": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedOriginUrlWithBadDomain with data set #2": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedOriginUrlWithBadDomain with data set #5": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedOriginUrlWithBadDomain with data set #4": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedOriginUrlWithBadDomain with data set #3": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedOriginUrlWithBadDomain with data set #1": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedOriginUrlWithBadDomain with data set #0": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOriginPortWithZero": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testResourceCnamesAreSavedAsLowercase": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedOriginUrlWithLocalhostIp": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOriginTimeoutTooLarge": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithIgnoredQueryParamsUpperLimit": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testExistingAccountIsUsed": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedUnallowedOriginDomain": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithUrl": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailWhenOriginIsNotOkForBucket": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithIgnoredQueryParamsContainingDuplicates": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedMp4": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithBucketName": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailsWhenAddingExistingCnameWithDifferentCase": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedUnallowedCnameDomain": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithHttpsRedirectCodeNotSetTreatedAsDisabled": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testStreamingPlaylistBypass": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithCnameLong64Bytes": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testForwardHostHeader": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testCertificateRequestIsCreatedWithInstantSsl": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithCnameLong65Bytes": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testCertificateRequestIsNotCreatedWithNoCustomCnames": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedGroup": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedOriginUrlWithPrivateRangeIp": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testStreamingPlaylistBypassEnabledForStreamOrigin": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailWhenBucketIsNotOkForOrigin": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithInvalidHttpsRedirectCode": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOriginTimeoutTooSmall": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOk": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testCertificateRequestIsNotCreatedWithNoCnames": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithHttpsRedirectCodeZeroTreatsAsNull": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testAccountIsCreated": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithIgnoredQueryParamsTooLong": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testInstantSslEnabled": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithIgnoredQueryParamsHavingTooMuchItems": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithRateLimitContentDispositionOriginHeaders with data set \"some set 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithRateLimitContentDispositionOriginHeaders with data set \"none set 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithRateLimitContentDispositionOriginHeaders with data set \"none set 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithRateLimitContentDispositionOriginHeaders with data set \"all set 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithRateLimitContentDispositionOriginHeaders with data set \"some set 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithRateLimitContentDispositionOriginHeaders with data set \"set with number 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithRateLimitContentDispositionOriginHeaders with data set \"all set 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithRateLimitContentDispositionOriginHeaders with data set \"all set 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithRateLimitContentDispositionOriginHeaders with data set \"set with number 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithRateLimitContentDispositionOriginHeaders with data set \"set with number 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithRateLimitContentDispositionOriginHeaders with data set \"set with number 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithIgnoredQueryParamsNotSet": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithIgnoredQueryParamsContainingMalformedName": 3, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\ResourceDetailInfoTest::testFromResourceWithNoAccount": 4, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\ResourceDetailInfoTest::testNewInstance": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Domain\\Finder\\ResourcesForPermanentRemoveFinderTest::testSuspend": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testOk": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\ExternalApi\\CertificateBucket\\CertificateBucketTest::testSave": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testWithOnlyOneAssignedCertificate": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testWithOneAssignedAndOneBackupCertificate": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testMultipleResources": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testWithAssignedIndexLowerThanMaxIndex": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testImportantAccountExcluded": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\Legacy\\Delete\\ResourceDeleterTest::testEnqueueOldCertificatesForRenewal": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Core\\Application\\GraphQL\\GraphQLClientTest::testWorks": 4, "Warning": 6, "Cdn77\\NxgApi\\Tests\\Unit\\Clap\\AccountFinderTest::testFindVipAndTopReturnsCorrectIds": 4, "Cdn77\\NxgApi\\Tests\\Unit\\Clap\\AccountFinderTest::testBothTypesSet": 4, "Cdn77\\NxgApi\\Tests\\Unit\\Clap\\AccountFinderTest::testOnlyVip": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testLimit": 3, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourcesControllerTest::testWithFilter": 3, "Cdn77\\NxgApi\\Tests\\Unit\\Log\\Formatter\\RequestFormatterTest::testLengthLimit": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testOk with data set \"empty -> some header\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testOk with data set \"some header -> empty\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testOk with data set \"empty -> empty\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testOk with data set \"some header -> another header\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFailWhenResponseHeadersMissing": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"Invalid name with colon\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"bad type 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"Invalid value with backslash\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"bad type 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"Forbidden header name\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"bad type 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"bad type 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"bad type 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"bad type 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"bad type 0\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"bad type 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"bad type 4\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"bad type 0\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"bad type 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"bad type 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"bad type 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Invalid value with backslash\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Invalid name with colon\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"bad type 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFailWhenResponseHeadersMissing": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFailType with data set \"bad type 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFailType with data set \"bad type 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFailType with data set \"bad type 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFailType with data set \"bad type 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFailType with data set \"bad type 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFailType with data set \"bad type 0\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFailType with data set \"bad type 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\ListControllerTest::testAllExistingResourcesWithSuspended": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\ListControllerTest::testAllExistingResources": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\DetailControllerTest::testWithIgnoredQueryParams": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\DetailControllerTest::testExistingResource": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\AddTest::testLegacy": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\AddTest::testOkWithMultipleOrigin": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\AddTest::testOkWithSingleOrigin": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\DomainLookupControllerTest::testLookupByCdnUrl": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\DomainLookupControllerTest::testLookupByCname": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GeoLocationProtection\\EditTest::testInvalidCountryCode": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GeoLocationProtection\\EditTest::testAllowedNotIsoCountryCode": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFailWhenCorsIsEnabled": 4, "Error": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"bad type\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 11\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 0\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 10\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 7\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 8\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 9\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden prefix\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testOk with data set \"disabled 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testOk with data set \"disabled 1\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad mode 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"not used -> turn on all files\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testOk with data set \"disabled\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testOk with data set \"mp4\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testOk with data set \"all files\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad mode 7\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad mode 8\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad combination 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad combination 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad combination 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad mode 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"mp4_only -> bad disabled 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad size 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"mp4_only -> bad combination 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"all files -> disabled\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"mp4 -> disabled\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"not used -> turn on mp4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"change mode\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"change size\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourcesControllerTest::testDeleted": 3, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourcesControllerTest::testSuspended": 3, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\ResourceDetailInfoTest::testFromResource": 4, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\ResourceDetailInfoTest::testFromResourceWithWafQuicCorsOriginHeaderVerifyOriginSsl": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size 9\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size 8\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size 7\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size range 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testOk with data set \"change size\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testSlicing with data set \"disabled\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testSlicing with data set \"mp4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testSlicing with data set \"all\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testOk with data set \"not used -> turn on mp4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testOk with data set \"change mode\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testOk with data set \"not used -> turn on all files\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testOk with data set \"mp4 -> disabled\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testOk with data set \"all files -> disabled\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testFail with data set \"not used -> bad mode 1\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testFail with data set \"mp4_only -> bad disabled 1\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testFail with data set \"not used -> bad size 1\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testFail with data set \"mp4_only -> size out of range 2\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testFail with data set \"not used -> bad combination 1\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testFail with data set \"mp4_only -> size out of range 1\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testFail with data set \"mp4_only -> bad combination 1\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testFailWhenFollowRedirectMissing": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad mode 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad mode 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad mode 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad mode 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad mode 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size range 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFailWhenFollowRedirectMissing": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testFailWhenSlicingMissing": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1 with data set \"name 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"valid resource ID without any record in DB 2\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"resource 1 variants 6\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"valid resource ID without any record in DB 3\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"resource 1 variants 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"unknown name 2\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"unknown name 1\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"resource 1 variants 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"resource 1 variants 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"unknown name 3\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"resource 1 variants 4\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"resource 1 variants 5\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"valid resource ID without any record in DB 1\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testWithSslWithoutFiles": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testArchiveCertificatesForIdInUsedIds": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testArchiveOnlyResourcesInUsedIds": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testDryRun": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testError": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testOnlyExpired": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithId": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\AddTest::testOk with data set \"empty 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\AddTest::testOk with data set \"data 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\AddTest::testOk with data set \"data 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\AddTest::testOk with data set \"data 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\AddTest::testOk with data set \"data 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\DatacenterListControllerTest::testCustomDcLocationsListResponseForResource": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\DatacenterListControllerTest::testNonCustomDcLocationsListResponseForResource": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testOkMultipleOrigins with data set \"false -> both true\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testOkMultipleOrigins with data set \"true -> first false, second true\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testOkMultipleOrigins with data set \"true -> first true, second false\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFailMultipleOrigins with data set \"true -> ok first, bad second\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFailMultipleOrigins with data set \"true -> bad first, ok second\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFailMultipleOrigins with data set \"false -> bad first, ok second\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFailMultipleOrigins with data set \"false -> ok first, bad second\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 9\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 9\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 8\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 8\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 7\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 7\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFailWhenSslVerifyDisableMissingMultipleOrigins": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFailWhenSslVerifyDisableMissing": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testOk with data set \"true -> false\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testOk with data set \"false -> true\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testOk with data set \"true -> true\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testOk with data set \"false -> false\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testOkMultipleOrigins with data set \"array 1, default 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testOkMultipleOrigins with data set \"array 1, array 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testOkMultipleOrigins with data set \"default both\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testOkMultipleOrigins with data set \"default 1, array 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testOk with data set \"single item\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testOk with data set \"double item\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testOk with data set \"empty\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslListControllerTest::testSuspendedAreNotIncludedByDefault": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslListControllerTest::testWithCustomTypeOnly": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslListControllerTest::testWithTypeAndExpirationSet": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslListControllerTest::testWithExpiration": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslListControllerTest::testWithInstantSslTypeOnly": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslListControllerTest::testWithNoParameters": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslListControllerTest::testSuspendedIsPresentWhenIncludeSuspendedIsSet": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\EditTest::testOriginsHavePriorityOverLegacy": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\EditTest::testSingleOrigin": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\EditTest::testLegacy": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\EditTest::testMultipleOrigins": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\EditTest::testDefaultValuesForOrigin": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testOkMultipleOrigins with data set \"data 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testOkMultipleOrigins with data set \"empty 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testOkMultipleOrigins with data set \"empty 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testOkMultipleOrigins with data set \"data 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testOk with data set \"empty 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testOk with data set \"data 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testOk with data set \"data 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testOk with data set \"empty 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\DeleteControllerTest::testDeletingExistingResource": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testOk with data set \"data 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testOk with data set \"data 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testOk with data set \"data 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testOk with data set \"empty 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testOk with data set \"data 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testOkMultipleOrigins with data set \"empty 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testOkMultipleOrigins with data set \"data 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testOkMultipleOrigins with data set \"data 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testOkMultipleOrigins with data set \"empty 1, value 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GeoLocationProtection\\EditTest::testDisableGeoLocations": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GeoLocationProtection\\EditTest::testGeoProtectionChangeBlacklistToWhitelist": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GeoLocationProtection\\EditTest::testInvalidGeoProtectionType": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GeoLocationProtection\\EditTest::testNoGeoLocations": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Domain\\ResourceSuspenderTest::testSuspend": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Domain\\ResourceSuspenderTest::testSuspendWithSuspendedResource": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Domain\\ResourceSuspenderTest::testUnsuspendWithUnsuspendedResource": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Domain\\ResourceSuspenderTest::testUnsuspend": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testOkMultipleOrigins with data set \"empty -> basedir\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testOkMultipleOrigins with data set \"basedir -> another basedir\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testOkMultipleOrigins with data set \"basedir -> empty\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testOk with data set \"basedir -> empty\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testOk with data set \"empty -> basedir 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testOk with data set \"empty -> empty\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testOk with data set \"empty -> basedir 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testOk with data set \"empty -> basedir 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testOk with data set \"basedir -> other basedir 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testOk with data set \"empty -> basedir 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFailWhenFollowRedirectMissingMultipleOrigins": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFailWhenFollowRedirectMissing": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"empty -> bad type 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"basedir -> bad type 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"basedir -> bad value 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"empty -> bad type 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"basedir -> bad type 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"empty -> bad value 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"basedir -> bad type 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"empty -> bad value 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"empty -> bad type 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"basedir -> bad value 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"empty -> bad value 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"empty -> bad type 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"basedir -> bad type 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"empty -> bad type 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"basedir -> bad value 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"empty -> bad value 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"basedir -> bad type 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"basedir -> bad value 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"basedir -> bad type 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"basedir -> bad value 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"empty -> bad type 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFailMultipleOrigins with data set \"empty -> bad type - first, second ok\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFailMultipleOrigins with data set \"empty -> bad value - first ok, second bad \"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFailMultipleOrigins with data set \"empty -> bad type - first ok, second bad\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFailMultipleOrigins with data set \"empty -> bad value - first bad, second ok \"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testTryDisableDisabledSecureToken": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testIgnoreChange": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testDisableSecureToken": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithoutSecureToken with data set \"bad 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithoutSecureToken with data set \"bad 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithoutSecureToken with data set \"bad 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithoutSecureToken with data set \"bad 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithoutSecureToken with data set \"bad 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithoutSecureToken with data set \"bad 8\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithoutSecureToken with data set \"bad 10\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithoutSecureToken with data set \"bad 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithoutSecureToken with data set \"bad 7\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithoutSecureToken with data set \"bad 9\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testSecureToken with data set \"all set 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testSecureToken with data set \"all set 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testSecureToken with data set \"all set 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testSecureToken with data set \"all set 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testSecureToken with data set \"required params\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testSecureToken with data set \"empty params\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithSecureToken with data set \"bad 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithSecureToken with data set \"bad 9\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithSecureToken with data set \"bad 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithSecureToken with data set \"bad 7\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithSecureToken with data set \"bad 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithSecureToken with data set \"bad 8\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithSecureToken with data set \"bad 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithSecureToken with data set \"bad 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithSecureToken with data set \"bad 10\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithSecureToken with data set \"bad 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testOkSecureTokenIgnoredWhenTypeIsNone": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testOkWithSecureToken with data set \"some set 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testOkWithSecureToken with data set \"some set 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testOkWithSecureToken with data set \"some set 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testOkWithSecureToken with data set \"required set 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testOkWithSecureToken with data set \"all set 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testOkWithSecureTokenAsNull": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testOk with data set \"true -> false\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testOk with data set \"false -> false\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testOk with data set \"true -> true\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testOk with data set \"false -> true\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 8\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 7\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 9\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 9\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 8\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 7\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testSslFileAlreadyExists": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithInvalidCertificatePair": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithExistingPreviousCertificateFiles": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithNoPreviousCertificate": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithMultipleCertificateConfigurations with data set #4": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithMultipleCertificateConfigurations with data set #2": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithMultipleCertificateConfigurations with data set #1": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithMultipleCertificateConfigurations with data set #3": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithMultipleCertificateConfigurations with data set #0": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithInvalidCertificateChain": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\HotlinkProtection\\EditTest::testHotlinkProtectionChangeBlacklistToWhitelist": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\HotlinkProtection\\EditTest::testDisableHotlinkProtectionWithEmptyDenied": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\HotlinkProtection\\EditTest::testDisableHotlinkProtection": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\HotlinkProtection\\EditTest::testHotlinkProtectionWithNoAddresses": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\HotlinkProtection\\EditTest::testHotlinkProtectionWithInvalidAddress": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\HotlinkProtection\\EditTest::testInvalidHotlinkProtectionType": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\HotlinkProtection\\EditTest::testHotlinkProtectionWithTooManyAddresses": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditFailWhenInstantSslIsEnabledWithSslCertificateSetup": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testCertificateRequestIsCancelledWhenInstantSslIsDisabled": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWithExistingSslFilesAndMissingSsl": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWithInvalidCertificateChain": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWhereSslFilesAreLeftIntactWhenSslIsDeleted": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testExistingCertificateIsUntouchedAndSslNotRemovedWhenInstantSslDisabledAndNoCnamesAreGiven": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditRemoveSslWithSslNotSet": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testInstantSslEnabled": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testCertificateRequestIsNotCreatedWhenNoCnamesAreGiven": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testSslIsRemovedWhenNoCustomCnamesAreGiven": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testCertificateRequestIsNotCreatedAndSslRemovedWhenNoCustomCnamesAreGiven": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testCertificateRequestIsCreatedForCraWithOneDomain": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditRemoveExistingSsl": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWithInvalidCertificatePair": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testCertificateRequestIsNotCreatedWhenCnamesAreChangedForInstantSslDisabled": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWhenInstantSslIsDisabledWithSslCertificateAndKeySetToNull with data set #0": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWhenInstantSslIsDisabledWithSslCertificateAndKeySetToNull with data set #1": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWhenInstantSslIsDisabledWithSslCertificateAndKeySetToNull with data set #2": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testCertificateRequestIsCreatedAndSslNotRemovedWhenCnamesAreAddedForInstantSsl": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWithMultipleCertificateConfigurations with data set #1": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWithMultipleCertificateConfigurations with data set #3": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWithMultipleCertificateConfigurations with data set #4": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWithMultipleCertificateConfigurations with data set #2": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWithMultipleCertificateConfigurations with data set #0": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testCertificateRequestIsCreatedWhenCnamesAreRemovedForInstantSsl": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testCertificateRequestIsCancelledWhenCnamesAreRemoved": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testInstantSslRemainsEnabled with data set #2": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testInstantSslRemainsEnabled with data set #0": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testInstantSslRemainsEnabled with data set #1": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testCertificateRequestIsCreatedForCraWithMultipleDomains": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditChangeCustomCertificateToInstantSslWithEmptySslField with data set #2": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditChangeCustomCertificateToInstantSslWithEmptySslField with data set #0": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditChangeCustomCertificateToInstantSslWithEmptySslField with data set #1": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testEditResourceWithExistingPreviousCertificateFiles": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testCertificateRequestIsNotCreatedWhenNoCnamesAreBeingEditedForInstantSsl": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testEditResourceSslWithNoPreviousCertificateSet": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditFailWhenInstantSslSettingsAreMissingForSslSetup": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissing with data set \"data 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissing with data set \"empty 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissing with data set \"empty 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissing with data set \"data 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 12\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 12\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 8\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 9\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"origin with codes -> bad combination 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 9\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad combination 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 7\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 9\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 8\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 9\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 11\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 9\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 10\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 7\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 11\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 7\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 7\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 9\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 11\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 7\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 7\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 8\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 10\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 10\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 12\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"only enabled -> off\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"not used -> turn on enabled with codes 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"enabled with codes -> off\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"only enabled -> enabled with codes 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"enabled with codes -> only enabled\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"not used -> turn on enabled with codes 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"not used -> turn on enabled\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"not used -> off\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"only enabled -> enabled with codes 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"only enabled -> only enabled\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"enabled with codes -> enabled with changed codes\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"basedir -> bad type 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"basedir -> bad value 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"basedir -> bad type 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"empty -> bad value 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"empty -> bad type 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"basedir -> bad type 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"empty -> bad type 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"basedir -> bad type 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"empty -> bad type 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"basedir -> bad value 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"basedir -> bad type 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"basedir -> bad value 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"empty -> bad value 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"basedir -> bad value 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"empty -> bad type 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"basedir -> bad type 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"empty -> bad type 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"empty -> bad value 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"empty -> bad type 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"empty -> bad value 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"basedir -> bad value 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testOk with data set \"basedir -> other basedir 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testOk with data set \"empty -> basedir 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testOk with data set \"basedir -> empty\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testOk with data set \"empty -> basedir 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testOk with data set \"empty -> basedir 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testOk with data set \"empty -> empty\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testOk with data set \"empty -> basedir 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SuspensionControllerTest::testSuspendActionWithSuspendedResource": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SuspensionControllerTest::testSuspendAction": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SuspensionControllerTest::testUnsuspendActionWithUnsuspendedResource": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SuspensionControllerTest::testUnsuspendAction": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOkMultipleOrigins with data set \"enabled with codes -> first off, second changed\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOkMultipleOrigins with data set \"enabled with codes -> enabled with changed codes\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissingMultipleOrigins with data set \"data 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissingMultipleOrigins with data set \"empty 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissingMultipleOrigins with data set \"empty 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissingMultipleOrigins with data set \"data 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissing with data set \"empty 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissing with data set \"empty 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissing with data set \"data 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissing with data set \"data 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOk with data set \"enabled with codes -> off\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOk with data set \"only enabled -> enabled with codes 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOk with data set \"enabled with codes -> only enabled\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOk with data set \"enabled with codes -> enabled with changed codes\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOk with data set \"not used -> off\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOk with data set \"not used -> turn on enabled\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOk with data set \"only enabled -> only enabled\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOk with data set \"only enabled -> enabled with codes 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOk with data set \"only enabled -> off\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOk with data set \"not used -> turn on enabled with codes 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOk with data set \"not used -> turn on enabled with codes 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 11\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 7\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 12\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"origin with codes -> bad combination 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 7\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 9\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 7\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 9\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 9\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad combination 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 7\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 8\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 10\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 12\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 9\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 11\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 10\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 11\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 9\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 9\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 8\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 7\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad combination 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 8\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 5\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 4\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 10\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 7\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 6\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 12\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailMultiple with data set \"not used -> first bad codes, second ok\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailMultiple with data set \"not used -> first bad, second ok\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailMultiple with data set \"not used -> first ok, second bad\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailMultiple with data set \"not used -> first ok, second bad codes\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testOkMultipleOrigins with data set \"default 1, value 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testOkMultipleOrigins with data set \"value 1, value 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testOkMultipleOrigins with data set \"value 1, default 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testOkMultipleOrigins with data set \"default both\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testOk with data set \"timeout min\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testOk with data set \"some timeout\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testOk with data set \"default\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testOk with data set \"timeout max\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GetCertificateControllerTest::testSingleResourceWithMultipleCertificatesWhenAssignedIsMissing": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GetCertificateControllerTest::testMultipleResourcesWithSingleCertificate": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GetCertificateControllerTest::testMultipleResourcesWhenOnlyOneHasCertificate": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GetCertificateControllerTest::testSingleResourceWithoutCertificate": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GetCertificateControllerTest::testSingleResourceWithSingleCertificate": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GetCertificateControllerTest::testSingleResourceWithMultipleCertificatesWhenAssignedIsNotTheLast": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GetCertificateControllerTest::testSingleResourceWithMultipleHistoryCertificatesButNoneAssigned": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GetCertificateControllerTest::testSingleResourceWithMultipleCertificates": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testOk with data set \"port min\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testOk with data set \"some port\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testOk with data set \"default\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testOk with data set \"port max\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testOkMultipleOrigins with data set \"value 1, value 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testOkMultipleOrigins with data set \"default 1, value 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testOkMultipleOrigins with data set \"default both\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testOkMultipleOrigins with data set \"value 1, default 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testOk with data set \"empty 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testOk with data set \"data 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testOk with data set \"empty 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testOk with data set \"data 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testFailWithMissingMainPriority": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testOk with data set \"priority min\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testFailWithMultipleMainPriority": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testOkMultipleOrigins with data set \"min 1, some 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testFailWithMultipleOriginsWithSamePriority": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithInstantSslWithPendingRequestAndNoExistingResult": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithAssignedFile with data set \"instant\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithAssignedFile with data set \"custom\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithNoSsl": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithInstantSslWithNoRequest": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithMultipleAssignedFiles with data set \"letsencrypt and custom with previous assigned\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithMultipleAssignedFiles with data set \"only letsencrypt with last assigned\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithMultipleAssignedFiles with data set \"custom and letsencrypt with previous assigned\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithMultipleAssignedFiles with data set \"only custom with last assigned\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithInstantSslWithPendingRequestAndFailedResult with data set \"generating error then validation error\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithInstantSslWithPendingRequestAndFailedResult with data set \"single generating error\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithInstantSslWithPendingRequestAndFailedResult with data set \"single validation error\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithInstantSslWithPendingRequestAndFailedResult with data set \"validation error then generating error\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithAssignedCertificateAndInstantSslWithPendingRequestAndNoExistingResult with data set \"only custom\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithAssignedCertificateAndInstantSslWithPendingRequestAndNoExistingResult with data set \"only letsencrypt\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithAssignedCertificateAndInstantSslWithPendingRequestAndNoExistingResult with data set \"mixed certificates\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithInstantSslWithCancelledRequest": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithoutSslFile": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ServersStatusControllerTest::testWithNoServersInGroup": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ServersStatusControllerTest::testWithCustomLocation": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ServersStatusControllerTest::testWithCustomLocationWithNotMatchingGroups": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ServersStatusControllerTest::testWithExistingServers": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EnableDatacentersControllerTest::testEnableLocations": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\IpProtection\\EditTest::testDisableIpProtection": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\IpProtection\\EditTest::testIpProtectionChangeBlacklistToWhitelist": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\IpProtection\\EditTest::testIpProtectionWholeNetwork": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\IpProtection\\EditTest::testInvalidIpProtectionType": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\IpProtection\\EditTest::testIpProtectionWithNoAddresses": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\IpProtection\\EditTest::testInvalidIpProtectionNetwork": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Entity\\Legacy\\Id\\ResourceIdGeneratorTest::testGeneratesValidIds": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithIdAndCdnUrl": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testOnlyInactiveResources": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testOnlyResourcesWithInactiveSsl": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testWithFilesWithoutSsl": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testWithOneFileWithoutSsl": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\ExternalApi\\CertificateBucket\\CertificateBucketTest::testGetLastMainCertificatePairIncomplete": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\ExternalApi\\CertificateBucket\\CertificateBucketTest::testGetLastMainCertificatePairNoFiles": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Domain\\Finder\\CertificateBucketMainCertificateTest::testGetLastMainCertificatePairNoFiles": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Domain\\Finder\\CertificateBucketMainCertificateTest::testGetLastMainCertificatePairIncomplete": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Clap\\OriginTest::testOk": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Clap\\OriginUpdaterTest::testOk": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Clap\\OriginUpdaterTest::testResourceWithMultipleOrigins": 5, "Cdn77\\NxgApi\\Tests\\Functional\\Clap\\OriginUpdaterTest::testResourceWithMultipleOriginsAndOneWithClapId": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Clap\\OriginUpdaterTest::testMultipleResources": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Clap\\OriginUpdaterTest::testMultipleResourcesWithoutClapOriginId": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testFailsOnTriggerWhenAddingExistingCname": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testFailsOnTriggerWhenAddingExistingCname with data set \"test 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testFailsOnTriggerWhenAddingExistingCname with data set \"test 4\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testFailsOnTriggerWhenAddingExistingCname with data set \"test 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testFailsWhenAddingExistingCnameWithDifferentCase": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname with data set \"test 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname with data set \"case 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsWhenAddingExistingCnameWithDifferentCase": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname with data set \"duplicate-within-new-list-itself\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname with data set \"case insensitive 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testFailsOnTriggerWhenAddingExistingCname with data set \"case 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testFailsOnTriggerWhenAddingExistingCname with data set \"case 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testOkWithEmptyCname": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testOkWithEmptyCname": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname with data set \"duplicate-with-surrounding-whitespace 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname with data set \"test 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname with data set \"test 3\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname with data set \"case insensitive 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname with data set \"duplicate-with-surrounding-whitespace 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testFailsOnTriggerWhenAddingExistingCname with data set \"test 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testFailsOnTriggerWhenAddingExistingCname with data set \"duplicate-with-surrounding-whitespace 1\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testFailsOnTriggerWhenAddingExistingCname with data set \"duplicate-with-surrounding-whitespace 2\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\CreateControllerTest::testTooOldRequestCancelled": 4, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\CreateControllerTest::testEnqueuedResultWithValidationError": 3, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\CreateControllerTest::testEmptyValues": 3, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\CreateControllerTest::testSuccessfulResult": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\LetsEncrypt\\RequestManagerTest::testCreateAndScheduleRequest": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\LetsEncrypt\\RequestManagerTest::testClearQueue": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\LetsEncrypt\\RequestManagerTest::testEnqueue": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\LetsEncrypt\\RequestManagerTest::testCancelAllPendingRequests": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\LetsEncrypt\\RenewalManagerTest::testEnqueueCertificatesRenewalForSpecificResources": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\LetsEncrypt\\RenewalManagerTest::testEnqueueOldCertificatesForRenewal": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\LetsEncrypt\\RenewalManagerTest::testEnqueueCertificatesRenewalForSpecificResourcesAsap": 4, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\CreateControllerTest::testRejectsResultForFinishedRequest": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\LetsEncrypt\\RenewalManagerTest::testEnqueueOldCertificatesForRenewalWhenTooOldCancelledIsNotTheLastRequest": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithInstantSslWithCancelledRequestWhenActiveCertificate": 3, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\ListControllerTest::testWithInactiveResources": 3, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\ListControllerTest::testWithInactiveSuspendedResource": 4, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\ListControllerTest::testWithInactiveDeletedResource": 3, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\ListControllerTest::testEmptyQueue": 3, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\ListControllerTest::testWithInactiveDeletedResourceLikeProduction": 3, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\ListControllerProductionLikeTest::testWithInactiveDeletedResourceLikeProduction": 3, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\ListControllerProductionLikeTest::testWithInactiveDeletedResourceWithoutFlushShouldFail": 1, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Console\\InactiveTest::testWithInactiveDeletedResource": 3, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Console\\InactiveTest::testWithInactiveSuspendedResource": 3, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Console\\InactiveResourceCancellationCommandTest::testWithInactiveDeletedResource": 4, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Console\\InactiveResourceCancellationCommandTest::testLimit": 4, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Console\\InactiveResourceCancellationCommandTest::testWithInactiveResources": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Console\\ResourceOriginComparisonCommandTest::testCompareWithDifferences": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Console\\ResourceOriginComparisonCommandTest::testCompareWithMatchingData": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\LoadControllerTest::testLoadForMultipleStoredCertificates": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\LoadControllerTest::testLoadForSpecificCertificate": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\LoadControllerTest::testLoadWhenAccountUuidDirMissing": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\LoadControllerTest::testLoadWhenAccountDirMissing": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteWithInvalidAccountId": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteWithUnknownAccount": 1, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteOnePairFromMultiple": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteWithInvalidUuid": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteWithUnknownUuid": 1, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteLastPair": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testHandlerDirectly": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithEmptyUuid": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testFailAddInvalidCertificatePair": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\LoadControllerTest::testLoadWithInvalidUuid with data set \"empty string\"": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testRewriteCertificateWithSameUuid": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithEmptyKey": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithEmptyCertificate": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testAddCertificateToNewAccount": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testAddCertificateToExistingAccount": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteWithInvalidUuidFormat with data set \"invalid characters\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteWithInvalidUuidFormat with data set \"invalid format\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteWithInvalidUuidFormat with data set \"uuid v1\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteWithInvalidUuidFormat with data set \"uuid v5\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteWithInvalidUuidFormat with data set \"empty string\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteWithInvalidUuidFormat with data set \"uuid v3\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\LoadControllerTest::testLoadWithEmptyKeyFile": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\LoadControllerTest::testLoadWhenNoKeysExist": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithInvalidAccountId": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithInvalidUuid with data set \"empty string\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithInvalidUuid with data set \"invalid format\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithInvalidUuid with data set \"uuid v1\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithInvalidUuid with data set \"uuid v5\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithInvalidUuid with data set \"uuid v3\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithInvalidUuid with data set \"invalid characters\"": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteOnePrivateKeyFromMultiple": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteLastAccountPrivateKey": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testAddPrivateKeyToExistingAccount": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testAddPrivateKeyToNewAccount": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testRewritePrivateKeyWithSameUuid": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithUuidAsPrivateKey": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithResourcesUnderDifferentAccounts": 4, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Groups\\ListControllerTest::testWorksWithNoGroups": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Groups\\ListControllerTest::testWorksWithExistingGroups": 3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFail with data set \"Over limit\"": 3}, "times": {"Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testRejectsTooLongQueryParams": 0.054, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testPartialEdit": 0.06, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testOriginTimeoutTooSmall": 0.128, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testOriginUrlWithLocalhostIp": 0.306, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSwitchStreamOriginToNonStreamOrigin": 0.245, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testUpdatingToEmptyCnames": 0.077, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testGroupChangeToLsGroup": 0.065, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testResourceEditWithBucketOriginEnabled": 0.151, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSwitchNonStreamOriginToStreamOrigin": 0.135, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testDisablingOriginTimeout": 0.055, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testResourceEditBucketFailWithInvalidType": 0.056, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailedUnallowedCname": 0.13, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testResourceEditBucketFailsWithInvalidHost": 0.105, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testInvalidOriginPort": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailNotSupplyingOriginPort": 0.045, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailedUnallowedOriginDomain": 0.039, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testHttpsRedirectCodeChange": 0.061, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testAcceptsNullInHttpsRedirectCodeAndDoesNothing": 0.056, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testWorksOnUpdatingSelfCname": 0.063, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testResourceEditWithBucketOriginDisabled": 0.06, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testUnprocessableModification": 0.367, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testChangingAccountCreatesAccount": 0.342, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testChangingIgnoredQueryParams": 0.148, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSettingIgnoredQueryParamsWhenNoExisted": 0.058, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testResettingOriginPortWorks": 0.275, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testResourceForEditNotFound": 0.123, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testEditFailsWithBadDomain with data set #5": 0.215, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testEditFailsWithBadDomain with data set #3": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testEditFailsWithBadDomain with data set #1": 0.049, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testEditFailsWithBadDomain with data set #0": 0.341, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testEditFailsWithBadDomain with data set #2": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testEditFailsWithBadDomain with data set #4": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testRejectsInvalidHttpsRedirectCode": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testOriginUrlWithReservedIp": 0.254, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testForwardHostHeader": 0.272, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailsWhenAddingExistingCnameWithDifferentCase": 0.061, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testChangingIgnoredQueryParamsWithTooManyItems": 0.248, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testRejectsInvalidQueryParamFormat": 0.039, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testAcceptsZeroToResetHttpsRedirectCode": 0.138, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testForwardHostHeaderIsNotMandatoryAndNotReset": 0.061, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSettingIgnoredQueryParamsWithUpperLimit": 0.111, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testChangingOriginTimeout": 0.139, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testEditProtectionOnly": 0.169, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testStreamingPlaylistBypass": 0.141, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSettingOriginHeaders with data set \"set data 1\"": 0.056, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSettingOriginHeaders with data set \"set empty 3\"": 0.15, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSettingOriginHeaders with data set \"change\"": 0.336, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSettingOriginHeaders with data set \"set empty 2\"": 0.055, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSettingOriginHeaders with data set \"set empty 1\"": 0.054, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSettingOriginHeaders with data set \"set data 2\"": 0.056, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testChangingAccount": 0.057, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailureOnDuplicateCname": 0.063, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testOriginTimeoutTooLarge": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testCustomLocationIsPreservedWhenGroupIsUnchanged": 0.067, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testPartialInput": 0.06, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testRejectsDuplicateQueryParams": 0.042, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testEdit": 0.08, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testNoChangeIsDoneWhenIgnoredQueryParamsIsNotSet": 0.062, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testWithCnameLong65Bytes": 0.125, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testCustomLocationIsDeletedWhenGroupIsChanged": 0.068, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 7\"": 0.032, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 5\"": 0.211, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 4\"": 0.323, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 9\"": 0.033, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 10\"": 0.03, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 2\"": 0.031, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 8\"": 0.213, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 11\"": 0.031, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 12\"": 0.09, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 6\"": 0.033, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 1\"": 0.033, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailSettingOriginHeaders with data set \"bad data 3\"": 0.039, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testWithCnameLong64Bytes": 0.057, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSettingCustomData": 0.246, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testNullingCustomData": 0.068, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testFailMissingParameters": 0.042, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testChangingAccountToSameAccountDoesNothing": 0.057, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testStreamingPlaylistBypassIsNotMandatoryAndNotReset": 0.06, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\responseHeaders\\EditTest::testOk with data set \"empty -> some header\"": 0.583, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\responseHeaders\\EditTest::testOk with data set \"some header -> empty\"": 0.107, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\responseHeaders\\EditTest::testOk with data set \"some header -> another header\"": 0.1, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\responseHeaders\\EditTest::testOk with data set \"empty -> empty\"": 0.133, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\responseHeaders\\EditTest::testFail with data set \"bad type 1\"": 0.058, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\responseHeaders\\EditTest::testFail with data set \"bad type 2\"": 0.082, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\responseHeaders\\EditTest::testFail with data set \"bad type 6\"": 0.055, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\responseHeaders\\EditTest::testFail with data set \"bad type 0\"": 0.059, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\responseHeaders\\EditTest::testFail with data set \"bad type 4\"": 0.074, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\responseHeaders\\EditTest::testFail with data set \"Forbidden header name\"": 0.051, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\responseHeaders\\EditTest::testFail with data set \"bad type 5\"": 0.436, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\responseHeaders\\EditTest::testFail with data set \"Invalid name with colon\"": 0.284, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\responseHeaders\\EditTest::testFail with data set \"Invalid value with backslash\"": 0.047, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\responseHeaders\\EditTest::testFail with data set \"bad type 3\"": 0.304, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\responseHeaders\\EditTest::testFailWhenResponseHeadersMissing": 0.53, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithNoPreviousCertificate": 0.234, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testSslFileAlreadyExists": 0.365, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithExistingPreviousCertificateFiles": 0.249, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithInvalidCertificatePair": 0.369, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithInvalidCertificateChain": 0.508, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithMultipleCertificateConfigurations with data set #2": 0.093, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithMultipleCertificateConfigurations with data set #1": 0.089, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithMultipleCertificateConfigurations with data set #3": 0.277, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithMultipleCertificateConfigurations with data set #0": 0.116, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithMultipleCertificateConfigurations with data set #4": 0.072, "Cdn77\\NxgApi\\Tests\\Functional\\Server\\Application\\Controller\\PauseServerControllerTest::testPauseAction": 0.034, "Cdn77\\NxgApi\\Tests\\Functional\\Server\\Application\\Controller\\PauseServerControllerTest::testPauseActionUpdatesExistingStatus": 0.228, "Cdn77\\NxgApi\\Tests\\Functional\\Server\\Application\\Controller\\PauseServerControllerTest::testPauseActionWithPausedServer": 0.025, "Cdn77\\NxgApi\\Tests\\Functional\\Server\\Application\\Controller\\PauseServerControllerTest::testWithNonExistentServer": 0.012, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceIpProtectionTest::test": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\NgxConfGen\\ResourcesSchemaTest::testInterfaceWorks": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\OriginBaseDirValidatorTest::testInvalidBaseDirWithSlashAtEnd": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\OriginBaseDirValidatorTest::testInvalidBaseDirWithSlashAtStart": 0.001, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\AddTest::testOkWithMultipleOrigin": 0.071, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\AddTest::testOkWithSingleOrigin": 0.14, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\AddTest::testWithMultipleOriginWhenFirstIsBad": 0.043, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\AddTest::testWithMultipleOriginWhenSecondIsBad": 0.039, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\AddTest::testBadWithSingleOrigin": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\AddTest::testFailedOriginUrlWithBadDomain with data set #2": 0.034, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\AddTest::testFailedOriginUrlWithBadDomain with data set #1": 0.103, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\AddTest::testFailedOriginUrlWithBadDomain with data set #5": 0.038, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\AddTest::testFailedOriginUrlWithBadDomain with data set #6": 0.035, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\AddTest::testFailedOriginUrlWithBadDomain with data set #0": 0.032, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\AddTest::testFailedOriginUrlWithBadDomain with data set #4": 0.106, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\AddTest::testFailedOriginUrlWithBadDomain with data set #3": 0.034, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\AddTest::testFailedOriginUrlWithBadDomain with data set #7": 0.27, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\AddTest::testLegacy": 0.053, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Payload\\ResultSchemaTest::testDeserialize": 0.037, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\ErrorsSchemaTest::testWithEmptyErrors": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\ErrorsSchemaTest::testWithErrorsPassedViaConstructor": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\ErrorsSchemaTest::testAddingErrors": 0, "Cdn77\\NxgApi\\Tests\\Unit\\HotlinkProtection\\Domain\\SetupHotlinkProtectionTest::testDisable": 0, "Cdn77\\NxgApi\\Tests\\Unit\\HotlinkProtection\\Domain\\SetupHotlinkProtectionTest::testUpdate": 0, "Cdn77\\NxgApi\\Tests\\Unit\\HotlinkProtection\\Domain\\SetupHotlinkProtectionTest::testEnableDisabledDenyEmptyOnly": 0, "Cdn77\\NxgApi\\Tests\\Unit\\HotlinkProtection\\Domain\\SetupHotlinkProtectionTest::testUpdateDisabled": 0, "Cdn77\\NxgApi\\Tests\\Unit\\HotlinkProtection\\Domain\\SetupHotlinkProtectionTest::testDisableEmptyReferer": 0, "Cdn77\\NxgApi\\Tests\\Unit\\HotlinkProtection\\Domain\\SetupHotlinkProtectionTest::testEnable": 0.007, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\DeleteControllerTest::testDeletingInvalidResource": 0.019, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\DeleteControllerTest::testDeletingExistingResource": 0.04, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ServerHttp2Test::testGettersAndSetters": 0.001, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithSecureToken with data set \"bad 7\"": 0.045, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithSecureToken with data set \"bad 3\"": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithSecureToken with data set \"bad 2\"": 0.105, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithSecureToken with data set \"bad 5\"": 0.046, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithSecureToken with data set \"bad 9\"": 0.049, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithSecureToken with data set \"bad 6\"": 0.048, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithSecureToken with data set \"bad 1\"": 0.046, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithSecureToken with data set \"bad 4\"": 0.048, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithSecureToken with data set \"bad 10\"": 0.051, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithSecureToken with data set \"bad 8\"": 0.309, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testDisableSecureToken": 0.063, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testSecureToken with data set \"all set 4\"": 0.057, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testSecureToken with data set \"all set 1\"": 0.336, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testSecureToken with data set \"all set 3\"": 0.06, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testSecureToken with data set \"all set 2\"": 0.061, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testSecureToken with data set \"required params\"": 0.06, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testSecureToken with data set \"empty params\"": 0.062, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testTryDisableDisabledSecureToken": 0.114, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testIgnoreChange": 0.061, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithoutSecureToken with data set \"bad 5\"": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithoutSecureToken with data set \"bad 6\"": 0.104, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithoutSecureToken with data set \"bad 10\"": 0.107, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithoutSecureToken with data set \"bad 2\"": 0.047, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithoutSecureToken with data set \"bad 3\"": 0.045, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithoutSecureToken with data set \"bad 9\"": 0.047, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithoutSecureToken with data set \"bad 4\"": 0.045, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithoutSecureToken with data set \"bad 7\"": 0.052, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithoutSecureToken with data set \"bad 8\"": 0.25, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\EditTest::testFailOnResourceWithoutSecureToken with data set \"bad 1\"": 0.296, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\LetsEncrypt\\RequestTest::testComplete": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\LetsEncrypt\\RequestTest::testInterfaceWorks": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\LetsEncrypt\\RequestTest::testCancel": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testStreamingPlaylistBypass": 0.157, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOriginTimeoutTooSmall": 0.226, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithCnameLong64Bytes": 0.055, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithIp": 0.148, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testCertificateRequestIsNotCreatedWithNoCustomCnames": 0.341, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithUrl": 0.15, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithInvalidHttpsRedirectCode": 0.034, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailWhenOriginIsNotOkForBucket": 0.042, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedUnallowedOriginDomain": 0.034, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithIgnoredQueryParamsNotSet": 0.339, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testCertificateRequestIsCreatedWithInstantSsl": 0.347, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testAccountIsCreated": 0.148, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testCertificateRequestIsNotCreatedWithNoCnames": 0.151, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testAccountExceptionForUnallowedCnameDomain": 0.059, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithRateLimitContentDispositionOriginHeaders with data set \"none set 1\"": 0.054, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithRateLimitContentDispositionOriginHeaders with data set \"some set 1\"": 0.054, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithRateLimitContentDispositionOriginHeaders with data set \"set with number 2\"": 0.053, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithRateLimitContentDispositionOriginHeaders with data set \"all set 2\"": 0.053, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithRateLimitContentDispositionOriginHeaders with data set \"set with number 3\"": 0.057, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithRateLimitContentDispositionOriginHeaders with data set \"some set 2\"": 0.052, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithRateLimitContentDispositionOriginHeaders with data set \"none set 2\"": 0.352, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithRateLimitContentDispositionOriginHeaders with data set \"set with number 4\"": 0.054, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithRateLimitContentDispositionOriginHeaders with data set \"all set 1\"": 0.148, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithRateLimitContentDispositionOriginHeaders with data set \"set with number 1\"": 0.054, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithRateLimitContentDispositionOriginHeaders with data set \"all set 3\"": 0.159, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithBucketName": 0.053, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOk": 0.057, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedOriginUrlWithLocalhostIp": 0.033, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testStreamingPlaylistBypassIsNotMandatory": 0.052, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOriginTimeout": 0.152, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithIgnoredQueryParamsContainingDuplicates": 0.034, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailsWhenAddingExistingCnameWithDifferentCase": 0.046, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithIgnoredQueryParamsContainingMalformedName": 0.034, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithHttpsRedirectCodeSet": 0.06, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOriginTimeoutTooLarge": 0.331, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedOriginUrlWithPrivateRangeIp": 0.131, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testStreamingPlaylistBypassShouldBeEnabledForStreamOrigin": 0.045, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedUnallowedCnameDomain": 0.035, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithHttpsRedirectCodeNotSetTreatedAsDisabled": 0.27, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithIgnoredQueryParamsUpperLimit": 0.208, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithIgnoredQueryParamsTooLong": 0.037, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithHttpsRedirectCodeZeroTreatsAsNull": 0.053, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedGroup": 0.32, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithIgnoredQueryParamsHavingTooMuchItems": 0.218, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testExistingAccountIsUsed": 0.058, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithCnameLong65Bytes": 0.233, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOriginPortWithZero": 0.056, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testForwardHostHeader": 0.056, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testResourceCnamesAreSavedAsLowercase": 0.054, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailWhenBucketIsNotOkForOrigin": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testInstantSslEnabled": 0.245, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOriginTimeoutWithZero": 0.058, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testWithIgnoredQueryParamsSet": 0.067, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testStreamingPlaylistBypassEnabledForStreamOrigin": 0.053, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedOriginUrlWithBadDomain with data set #1": 0.032, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedOriginUrlWithBadDomain with data set #0": 0.315, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedOriginUrlWithBadDomain with data set #4": 0.137, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedOriginUrlWithBadDomain with data set #2": 0.224, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedOriginUrlWithBadDomain with data set #3": 0.033, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedOriginUrlWithBadDomain with data set #5": 0.218, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testFailedMp4": 0.032, "Cdn77\\NxgApi\\Tests\\Unit\\Filter\\Resource\\DeletedResourceFilterTest::testResource": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Filter\\Resource\\DeletedResourceFilterTest::testNotResource": 0.006, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Certificate\\CertificateChainValidatorTest::testInvalidChain with data set #3": 0.002, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Certificate\\CertificateChainValidatorTest::testInvalidChain with data set #2": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Certificate\\CertificateChainValidatorTest::testInvalidChain with data set #0": 0.002, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Certificate\\CertificateChainValidatorTest::testInvalidChain with data set #1": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Certificate\\CertificateChainValidatorTest::testNoChain": 0.155, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Certificate\\CertificateChainValidatorTest::testValidChain with data set #1": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Certificate\\CertificateChainValidatorTest::testValidChain with data set #2": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Certificate\\CertificateChainValidatorTest::testValidChain with data set #0": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Resource\\Domain\\ResourceDatacentersResolverTest::testResolveForResource": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Resource\\Domain\\ResourceDatacentersResolverTest::testResolveForResourceWithCustomLocations": 0.002, "Cdn77\\NxgApi\\Tests\\Functional\\FullLogs\\Application\\Controller\\DisableControllerTest::testDisableOnAlreadyDisabled": 0.026, "Cdn77\\NxgApi\\Tests\\Functional\\FullLogs\\Application\\Controller\\DisableControllerTest::testResourceNotFound": 0.018, "Cdn77\\NxgApi\\Tests\\Functional\\FullLogs\\Application\\Controller\\DisableControllerTest::testDisable": 0.038, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceOriginTest::testInvalidTimeout": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceOriginTest::testInvalidOriginScheme": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceOriginTest::testIsStreamingOrigin with data set #1": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceOriginTest::testIsStreamingOrigin with data set #2": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceOriginTest::testIsStreamingOrigin with data set #3": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceOriginTest::testIsStreamingOrigin with data set #10": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceOriginTest::testIsStreamingOrigin with data set #13": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceOriginTest::testIsStreamingOrigin with data set #7": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceOriginTest::testIsStreamingOrigin with data set #6": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceOriginTest::testIsStreamingOrigin with data set #11": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceOriginTest::testIsStreamingOrigin with data set #12": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceOriginTest::testIsStreamingOrigin with data set #0": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceOriginTest::testIsStreamingOrigin with data set #4": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceOriginTest::testIsStreamingOrigin with data set #5": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceOriginTest::testIsStreamingOrigin with data set #8": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceOriginTest::testIsStreamingOrigin with data set #9": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceOriginTest::test": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceOriginTest::testInvalidOriginPort": 0, "Cdn77\\NxgApi\\Tests\\Functional\\FullLogs\\Application\\Controller\\EnableControllerTest::testEnableOnAlreadyEnabled": 0.232, "Cdn77\\NxgApi\\Tests\\Functional\\FullLogs\\Application\\Controller\\EnableControllerTest::testEnable": 0.126, "Cdn77\\NxgApi\\Tests\\Functional\\FullLogs\\Application\\Controller\\EnableControllerTest::testResourceNotFound": 0.017, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testOk with data set \"false -> false\"": 0.293, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testOk with data set \"true -> false\"": 0.33, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testOk with data set \"true -> true\"": 0.057, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testOk with data set \"false -> true\"": 0.337, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 6\"": 0.056, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 7\"": 0.051, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 8\"": 0.057, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 1\"": 0.058, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 3\"": 0.117, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 2\"": 0.046, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 1\"": 0.126, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 5\"": 0.052, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 9\"": 0.056, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 5\"": 0.056, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 4\"": 0.049, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 4\"": 0.258, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 6\"": 0.123, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 3\"": 0.063, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 2\"": 0.114, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 9\"": 0.259, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 8\"": 0.05, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 7\"": 0.048, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\LetsEncrypt\\DomainChooser\\DefaultDomainChooserTest::testChoose": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\LetsEncrypt\\DomainChooser\\DefaultDomainChooserTest::testChooseWithNoCnames": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\LetsEncrypt\\DomainChooser\\DefaultDomainChooserTest::testSupports": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceGeoProtectionLocationTest::test": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Groups\\GroupListEntryDetailTest::testFunctionality": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Server\\Application\\Controller\\UnpauseServerControllerTest::testWithNonExistentServer": 0.208, "Cdn77\\NxgApi\\Tests\\Functional\\Server\\Application\\Controller\\UnpauseServerControllerTest::testUnpauseActionWithUnpausedServer": 0.036, "Cdn77\\NxgApi\\Tests\\Functional\\Server\\Application\\Controller\\UnpauseServerControllerTest::testUnpauseActionUpdatesExistingStatus": 0.028, "Cdn77\\NxgApi\\Tests\\Functional\\Server\\Application\\Controller\\UnpauseServerControllerTest::testUnpauseAction": 0.028, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\ResourceDetailTest::testFunctionality": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\Ssl\\CertificatesCertificateResourceItemTest::test": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\ResourceFullLogStatusSchemaTest::testConstruct": 0.001, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\ListControllerTest::testEmptyQueue": 0.045, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\ListControllerTest::testWithMultipleTasks": 0.086, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\ListControllerTest::testWithDebugResourcesWithWrongResourceId": 0.128, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\ListControllerTest::testWithDebugResources": 0.352, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Internal\\Server\\Force\\ForceUpControllerTest::testForceUpAction": 0.026, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Internal\\Server\\Force\\ForceUpControllerTest::testForceUpActionWithNonExistentServer": 0.011, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Internal\\Server\\Force\\ForceUpControllerTest::testForceUpActionUpdatesExistingStatus": 0.029, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Internal\\Server\\Force\\ForceUpControllerTest::testForceUpActionWithForcedUpServer": 0.026, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFail with data set \"bad type 4\"": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFail with data set \"Forbidden header name Accept\"": 0.039, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFail with data set \"Invalid value with backslash\"": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFail with data set \"bad type 0\"": 0.293, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFail with data set \"Invalid name with colon\"": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFail with data set \"bad type 6\"": 0.039, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFail with data set \"bad type 2\"": 0.246, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFail with data set \"bad type 1\"": 0.105, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFail with data set \"bad type 3\"": 0.039, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFail with data set \"bad type 5\"": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFailMultipleOrigins with data set \"Forbidden header names Accept\"": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFailMultipleOrigins with data set \"bad type 1 - both bad\"": 0.231, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFailMultipleOrigins": 0.039, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFailMultipleOrigins with data set \"bad type 1 - first ok, second bad\"": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFailMultipleOrigins with data set \"bad type 1 - first bad, second ok\"": 0.048, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFailMultipleOrigins with data set \"Forbidden header names via and range\"": 0.105, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFailWhenOriginHeadersMissingMultipleOrigins": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testOk with data set \"empty\"": 0.06, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testOk with data set \"double item\"": 0.314, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testOk with data set \"single item\"": 0.064, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testOkMultipleOrigins with data set \"default 1, array 2\"": 0.137, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testOkMultipleOrigins with data set \"default both\"": 0.121, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testOkMultipleOrigins with data set \"array 1, default 2\"": 0.29, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testOkMultipleOrigins with data set \"array 1, array 2\"": 0.067, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFailWhenOriginHeadersMissing": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testOk with data set \"empty 1\"": 0.058, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testOk with data set \"data 2\"": 0.094, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testOk with data set \"data 4\"": 0.1, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testOk with data set \"data 1\"": 0.058, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testOk with data set \"data 3\"": 0.067, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testOkMultipleOrigins with data set \"data 1\"": 0.064, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testOkMultipleOrigins with data set \"empty 1\"": 0.118, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testOkMultipleOrigins with data set \"data 2\"": 0.298, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testOkMultipleOrigins with data set \"empty 1, value 2\"": 0.067, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testFailWhenBasedirMissingMultipleOrigins": 0.042, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testFail with data set \"bad value 2\"": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testFail with data set \"bad type 4\"": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testFail with data set \"bad type 1\"": 0.042, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testFail with data set \"bad type 2\"": 0.224, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testFail with data set \"bad type 6\"": 0.042, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testFail with data set \"bad value 5\"": 1.253, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testFail with data set \"bad type 3\"": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testFail with data set \"bad type 5\"": 0.26, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testFail with data set \"bad value 4\"": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testFail with data set \"bad value 1\"": 0.076, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testFail with data set \"bad value 3\"": 0.08, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testFailMultipleOrigins with data set \"bad type 1 - both bad\"": 0.251, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testFailMultipleOrigins with data set \"bad type 1 - first ok, second bad\"": 0.042, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testFailMultipleOrigins with data set \"bad type 1 - first bad, second ok\"": 0.085, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\AddTest::testFailWhenBasedirMissing": 0.04, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\VersionSchemaTest::testFunctionality": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Entity\\Legacy\\Id\\ResourceIdGeneratorTest::testGeneratesValidIds": 0.003, "Cdn77\\NxgApi\\Tests\\Functional\\Entity\\Legacy\\Id\\ResourceIdGeneratorTest::testIdIsGeneratedForNewEntity": 0.027, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissing with data set \"empty 1\"": 0.047, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissing with data set \"empty 2\"": 0.043, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissing with data set \"data 1\"": 0.043, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissing with data set \"data 2\"": 0.042, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 1\"": 0.103, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad combination 1\"": 0.047, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 1\"": 0.047, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 7\"": 0.059, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 6\"": 0.414, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 7\"": 0.237, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 1\"": 0.22, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 5\"": 0.08, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 10\"": 0.046, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 3\"": 0.031, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 12\"": 0.088, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 11\"": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 2\"": 0.098, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 4\"": 0.034, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 9\"": 0.046, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 2\"": 0.047, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 4\"": 0.035, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"origin with codes -> bad combination 1\"": 0.047, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 3\"": 0.078, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 5\"": 0.046, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 4\"": 0.145, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad combination 1\"": 0.046, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 1\"": 0.048, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 4\"": 0.079, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 10\"": 0.496, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 10\"": 0.222, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 2\"": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 12\"": 0.071, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 9\"": 0.054, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 9\"": 0.045, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 12\"": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 2\"": 0.066, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 3\"": 0.092, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 2\"": 0.219, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 11\"": 0.048, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 7\"": 0.107, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 9\"": 0.309, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 5\"": 0.243, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 5\"": 0.032, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 7\"": 0.349, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 3\"": 0.09, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 3\"": 0.037, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 7\"": 0.087, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 6\"": 0.048, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 11\"": 0.045, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 1\"": 0.03, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 2\"": 0.048, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 4\"": 0.035, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 6\"": 0.047, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 6\"": 0.139, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 4\"": 0.095, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 6\"": 0.049, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 8\"": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 3\"": 0.047, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 9\"": 0.084, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 6\"": 0.052, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 1\"": 0.036, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 8\"": 0.047, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 5\"": 0.033, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 9\"": 0.05, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 8\"": 0.253, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 5\"": 0.046, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 7\"": 0.091, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"only enabled -> enabled with codes 2\"": 0.253, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"not used -> off\"": 0.292, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"only enabled -> enabled with codes 1\"": 0.058, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"enabled with codes -> off\"": 0.058, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"only enabled -> only enabled\"": 0.055, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"not used -> turn on enabled with codes 1\"": 0.095, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"not used -> turn on enabled\"": 0.266, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"enabled with codes -> enabled with changed codes\"": 0.283, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"not used -> turn on enabled with codes 2\"": 0.1, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"only enabled -> off\"": 0.057, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"enabled with codes -> only enabled\"": 0.102, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testInvalidConstraint": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testInvalidFormats with data set #3": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testInvalidFormats with data set #5": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testInvalidFormats with data set #6": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testInvalidFormats with data set #7": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testInvalidFormats with data set #4": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testInvalidFormats with data set #9": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testInvalidFormats with data set #8": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testInvalidFormats with data set #1": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testInvalidFormats with data set #0": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testInvalidFormats with data set #2": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testValidFormats with data set #4": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testValidFormats with data set #16": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testValidFormats with data set #11": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testValidFormats with data set #15": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testValidFormats with data set #17": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testValidFormats with data set #5": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testValidFormats with data set #8": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testValidFormats with data set #6": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testValidFormats with data set #7": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testValidFormats with data set #13": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testValidFormats with data set #3": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testValidFormats with data set #0": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testValidFormats with data set #14": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testValidFormats with data set #9": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testValidFormats with data set #12": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testValidFormats with data set #2": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testValidFormats with data set #10": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testValidFormats with data set #1": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainValidatorTest::testAcceptsNull": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testInvalidConstraint": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testAcceptsNull": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testInvalidValues with data set #3": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testInvalidValues with data set #12": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testInvalidValues with data set #10": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testInvalidValues with data set #7": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testInvalidValues with data set #4": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testInvalidValues with data set #0": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testInvalidValues with data set #11": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testInvalidValues with data set #5": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testInvalidValues with data set #6": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testInvalidValues with data set #2": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testInvalidValues with data set #9": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testInvalidValues with data set #1": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testInvalidValues with data set #8": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testValidValues with data set #5": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testValidValues with data set #6": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testValidValues with data set #4": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testValidValues with data set #3": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testValidValues with data set #7": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testValidValues with data set #14": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testValidValues with data set #1": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testValidValues with data set #0": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testValidValues with data set #12": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testValidValues with data set #10": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testValidValues with data set #2": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testValidValues with data set #8": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testValidValues with data set #9": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testValidValues with data set #11": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testValidValues with data set #13": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\LocationGroupTest::testGettersAndSetters": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testFailWithSecureToken with data set \"bad 10\"": 0.034, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testFailWithSecureToken with data set \"bad 3\"": 0.035, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testFailWithSecureToken with data set \"bad 9\"": 0.06, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testFailWithSecureToken with data set \"bad 7\"": 0.035, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testFailWithSecureToken with data set \"bad 4\"": 0.237, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testFailWithSecureToken with data set \"bad 8\"": 0.035, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testFailWithSecureToken with data set \"bad 5\"": 0.036, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testFailWithSecureToken with data set \"bad 2\"": 0.268, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testFailWithSecureToken with data set \"bad 6\"": 0.213, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testFailWithSecureToken with data set \"bad 1\"": 2.05, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testOkWithSecureToken with data set \"some set 1\"": 0.079, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testOkWithSecureToken with data set \"all set 1\"": 0.084, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testOkWithSecureToken with data set \"some set 3\"": 0.155, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testOkWithSecureToken with data set \"required set 1\"": 0.077, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testOkWithSecureToken with data set \"some set 2\"": 0.057, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testOkWithSecureTokenAsNull": 0.053, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SecureToken\\AddTest::testOkSecureTokenIgnoredWhenTypeIsNone": 0.06, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Domain\\ResourceSuspenderTest::testUnsuspend": 0.028, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Domain\\ResourceSuspenderTest::testUnsuspendWithUnsuspendedResource": 0.024, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Domain\\ResourceSuspenderTest::testSuspendWithSuspendedResource": 0.026, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Domain\\ResourceSuspenderTest::testSuspend": 0.088, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\Legacy\\Certificate\\OpenSSLCertificateMetadataParserTest::testWithMultipleCertificateConfigurations with data set #1": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\Legacy\\Certificate\\OpenSSLCertificateMetadataParserTest::testWithMultipleCertificateConfigurations with data set #4": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\Legacy\\Certificate\\OpenSSLCertificateMetadataParserTest::testWithMultipleCertificateConfigurations with data set #3": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\Legacy\\Certificate\\OpenSSLCertificateMetadataParserTest::testWithMultipleCertificateConfigurations with data set #0": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\Legacy\\Certificate\\OpenSSLCertificateMetadataParserTest::testWithMultipleCertificateConfigurations with data set #2": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Account\\Application\\Controller\\DatacenterListControllerTest::testFindDatacenterLocationsForAccount": 0.091, "Cdn77\\NxgApi\\Tests\\Functional\\Account\\Application\\Controller\\DatacenterListControllerTest::testNoLocationsForAccount": 0.016, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testFail with data set \"bad type 2\"": 0.242, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testFail with data set \"bad type 1\"": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testFail with data set \"bad type 3\"": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testFail with data set \"bad value 1\"": 0.039, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testFail with data set \"bad value 2\"": 0.038, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testFail with data set \"bad type 5\"": 0.038, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testFail with data set \"bad type 6\"": 0.498, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testFail with data set \"bad type 4\"": 0.045, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testFail with data set \"bad type 0\"": 0.039, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testFailMultipleOrigins with data set \"bad type 1 - first ok, second bad\"": 0.048, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testFailMultipleOrigins with data set \"bad type 1 - first bad, second ok\"": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testFailMultipleOrigins with data set \"bad type 1 - both bad\"": 0.245, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testOk with data set \"timeout max\"": 0.057, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testOk with data set \"timeout min\"": 0.058, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testOk with data set \"some timeout\"": 0.069, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testOk with data set \"default\"": 0.057, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testFailWhenTimeoutMissing": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testOkMultipleOrigins with data set \"default 1, value 2\"": 0.12, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testOkMultipleOrigins with data set \"value 1, value 2\"": 0.302, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testOkMultipleOrigins with data set \"value 1, default 2\"": 0.286, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testOkMultipleOrigins with data set \"default both\"": 0.06, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Timeout\\AddTest::testFailWhenBasedirMissingMultipleOrigins": 0.039, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Dns\\Generator\\CustomLocationDetailTest::test": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\Server\\ServerStatusManagerTest::testBeingUpDoesNotGenerateLastDownEntry": 0.006, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\Server\\ServerStatusManagerTest::testWithExistingRecordsDoesNotAttemptToCreateAnother": 0.029, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\Server\\ServerStatusManagerTest::testBeingDownDoesNotUpdateWentDownAtButUpdatesReason": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\Server\\ServerStatusManagerTest::testWithNoExistingRecordsCreatesEntries": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\ResourceDetailInfoTest::testFromResource": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\ResourceDetailInfoTest::testFromResourceWithNoAccount": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\ResourceDetailInfoTest::testFromResourceWithWafQuicCorsOriginHeaderVerifyOriginSsl": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\ResourceDetailInfoTest::testNewInstance": 0.002, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\ListControllerTest::testAllExistingResourcesWithSuspended": 0.08, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\ListControllerTest::testWithNoResources": 0.262, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\ListControllerTest::testAllExistingResources": 0.342, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Internal\\Ip\\Status\\IpStatusSchemaTest::test": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Certificate\\CertificateValidatorTest::testInvalidCertificate with data set #2": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Certificate\\CertificateValidatorTest::testInvalidCertificate with data set #1": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Certificate\\CertificateValidatorTest::testInvalidCertificate with data set #0": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Certificate\\CertificateValidatorTest::testEmptyCertificate with data set #0": 0.002, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Certificate\\CertificateValidatorTest::testValidCertificate with data set #0": 0.002, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testAcceptsNull": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testInvalidConstraint": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testInvalidValues with data set #4": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testInvalidValues with data set #0": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testInvalidValues with data set #2": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testInvalidValues with data set #7": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testInvalidValues with data set #1": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testInvalidValues with data set #8": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testInvalidValues with data set #5": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testInvalidValues with data set #12": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testInvalidValues with data set #6": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testInvalidValues with data set #10": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testInvalidValues with data set #3": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testInvalidValues with data set #9": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testInvalidValues with data set #11": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testValidValues with data set #3": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testValidValues with data set #0": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testValidValues with data set #2": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testValidValues with data set #1": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"basedir -> bad value 2\"": 0.045, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"empty -> bad type 2\"": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"empty -> bad value 3\"": 0.302, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"basedir -> bad type 1\"": 0.123, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"basedir -> bad type 6\"": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"empty -> bad value 1\"": 0.053, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"empty -> bad value 2\"": 0.138, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"basedir -> bad type 2\"": 0.042, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"empty -> bad value 4\"": 0.067, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"empty -> bad type 1\"": 0.246, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"basedir -> bad type 5\"": 0.045, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"basedir -> bad type 4\"": 0.042, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"basedir -> bad value 5\"": 0.228, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"basedir -> bad value 1\"": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"empty -> bad type 6\"": 0.255, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"basedir -> bad value 3\"": 0.045, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"basedir -> bad type 3\"": 0.132, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"basedir -> bad value 4\"": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"empty -> bad type 4\"": 0.229, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"empty -> bad type 5\"": 0.047, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testFail with data set \"empty -> bad type 3\"": 0.052, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testOk with data set \"empty -> basedir 3\"": 0.055, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testOk with data set \"empty -> basedir 4\"": 0.141, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testOk with data set \"empty -> basedir 1\"": 0.058, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testOk with data set \"basedir -> other basedir 1\"": 0.158, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testOk with data set \"empty -> empty\"": 0.245, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testOk with data set \"basedir -> empty\"": 0.058, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\EditTest::testOk with data set \"empty -> basedir 2\"": 0.06, "Cdn77\\NxgApi\\Tests\\Unit\\Log\\Formatter\\RequestFormatterTest::testStrippingCertificates": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Log\\Formatter\\RequestFormatterTest::testBasicFormatting": 0, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourcesControllerTest::testWithFilter": 0.055, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourcesControllerTest::testDeleted": 0.138, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourcesControllerTest::testMissingOrigin": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourcesControllerTest::testWithNoData": 0.219, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourcesControllerTest::test": 0.138, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourcesControllerTest::testMissingMainOrigin": 0.072, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourcesControllerTest::testSuspended": 0.058, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourcesControllerTest::testWithWrongFilterParameter": 0.006, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\AccountTest::testWithResource": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\AccountTest::testEmpty": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceFullLogTest::testInterfaceWorks": 0.006, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Internal\\Server\\ServerDetailSchemaTest::test": 0.004, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Internal\\Server\\ServerDetailSchemaTest::testNullable": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\DTO\\NxgConfGen\\ResourceDTOTest::testInterfaceWorks with data set #1": 0, "Cdn77\\NxgApi\\Tests\\Unit\\DTO\\NxgConfGen\\ResourceDTOTest::testInterfaceWorks with data set #0": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Server\\Application\\Payload\\ResourceServerIdListSchemaTest::testSerialize": 0.002, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\HotlinkProtection\\RefererAddDetailTest::test": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Core\\Application\\Controller\\VersionControllerTest::testWorks": 0.237, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslListControllerTest::testSuspendedIsPresentWhenIncludeSuspendedIsSet": 0.057, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslListControllerTest::testWithTypeAndExpirationSet": 0.066, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslListControllerTest::testSuspendedAreNotIncludedByDefault": 0.056, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslListControllerTest::testWithExpiration": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslListControllerTest::testWithInstantSslTypeOnly": 0.07, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslListControllerTest::testWithMalformedExpiration": 0.007, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslListControllerTest::testWithInvalidType": 0.188, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslListControllerTest::testWithNoParameters": 0.072, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SslListControllerTest::testWithCustomTypeOnly": 0.304, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceRefererProtectionTest::testInvalidType": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceRefererProtectionTest::test": 0.002, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\ExternalApi\\CertificateBucket\\CertificateBucketTest::testSaveWrongContent": 0.298, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\ExternalApi\\CertificateBucket\\CertificateBucketTest::testSaveWriteProblem": 0.496, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\ExternalApi\\CertificateBucket\\CertificateBucketTest::testSave": 0.348, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithAssignedFile with data set \"instant\"": 0.27, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithAssignedFile with data set \"custom\"": 0.216, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithNoSsl": 0.032, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithInstantSslWithPendingRequestAndNoExistingResult": 0.036, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithInstantSslWithCancelledRequest": 0.033, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithInstantSslWithNoRequest": 0.035, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithAssignedCertificateAndInstantSslWithPendingRequestAndNoExistingResult with data set \"mixed certificates\"": 0.237, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithAssignedCertificateAndInstantSslWithPendingRequestAndNoExistingResult with data set \"only custom\"": 0.047, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithAssignedCertificateAndInstantSslWithPendingRequestAndNoExistingResult with data set \"only letsencrypt\"": 0.1, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithMultipleAssignedFiles with data set \"letsencrypt and custom with previous assigned\"": 0.055, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithMultipleAssignedFiles with data set \"custom and letsencrypt with previous assigned\"": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithMultipleAssignedFiles with data set \"only custom with last assigned\"": 0.298, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithMultipleAssignedFiles with data set \"only letsencrypt with last assigned\"": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithoutSslFile": 0.032, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithInstantSslWithPendingRequestAndFailedResult with data set \"generating error then validation error\"": 0.039, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithInstantSslWithPendingRequestAndFailedResult with data set \"single generating error\"": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithInstantSslWithPendingRequestAndFailedResult with data set \"validation error then generating error\"": 0.262, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithInstantSslWithPendingRequestAndFailedResult with data set \"single validation error\"": 0.135, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testOk with data set \"empty 1\"": 0.109, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testOk with data set \"data 1\"": 0.253, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testOk with data set \"empty 2\"": 0.061, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testOk with data set \"data 2\"": 0.065, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFailMultipleOrigins with data set \"bad both 1\"": 0.097, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFailMultipleOrigins with data set \"bad second 2\"": 0.02, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFailMultipleOrigins with data set \"bad first 2\"": 0.228, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFailMultipleOrigins with data set \"bad first 1\"": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFailMultipleOrigins with data set \"bad second 1\"": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFailMultipleOrigins with data set \"bad second 3\"": 0.042, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFailMultipleOrigins with data set \"bad first 3\"": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFailWhenFollowRedirectMissing": 0.292, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 3\"": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 8\"": 0.239, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad combination 1\"": 0.281, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad codes 11\"": 0.042, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad codes 2\"": 0.204, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad codes 13\"": 0.238, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 6\"": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad codes 6\"": 0.3, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 14\"": 0.101, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad combination 2\"": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 1\"": 0.25, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 11\"": 0.039, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 2\"": 0.276, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad codes 3\"": 0.02, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad codes 10\"": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad codes 9\"": 0.27, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 13\"": 0.24, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad codes 7\"": 0.305, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad codes 4\"": 0.02, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad codes 1\"": 0.02, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad codes 12\"": 0.039, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 4\"": 0.236, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad codes 8\"": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 5\"": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 12\"": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 7\"": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 10\"": 0.042, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 9\"": 0.045, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFail with data set \"bad codes 5\"": 0.019, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testOkMultipleOrigins with data set \"data 2\"": 0.335, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testOkMultipleOrigins with data set \"data 1\"": 0.061, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testOkMultipleOrigins with data set \"empty 2\"": 0.059, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testOkMultipleOrigins with data set \"empty 1\"": 0.113, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\AddTest::testFailWhenFollowRedirectMissingMultipleOrigins": 0.045, "Cdn77\\NxgApi\\Tests\\Unit\\Types\\ResourceCnamesArrayTypeTest::testName": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Types\\ResourceCnamesArrayTypeTest::testConvertToDatabaseValue with data set #2": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Types\\ResourceCnamesArrayTypeTest::testConvertToDatabaseValue with data set #4": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Types\\ResourceCnamesArrayTypeTest::testConvertToDatabaseValue with data set #1": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Types\\ResourceCnamesArrayTypeTest::testConvertToDatabaseValue with data set #3": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Types\\ResourceCnamesArrayTypeTest::testConvertToDatabaseValue with data set #0": 0.01, "Cdn77\\NxgApi\\Tests\\Unit\\Types\\ResourceCnamesArrayTypeTest::testGetSQLDeclaration": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Types\\ResourceCnamesArrayTypeTest::testConvertToPHPValue with data set #0": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Types\\ResourceCnamesArrayTypeTest::testConvertToPHPValue with data set #3": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Types\\ResourceCnamesArrayTypeTest::testConvertToPHPValue with data set #1": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Types\\ResourceCnamesArrayTypeTest::testConvertToPHPValue with data set #2": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Types\\ResourceCnamesArrayTypeTest::testConvertToPHPValue with data set #4": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\LetsEncrypt\\DomainChooser\\CraDomainChooserTest::testChooseWithNoCnames": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\LetsEncrypt\\DomainChooser\\CraDomainChooserTest::testChoose": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\LetsEncrypt\\DomainChooser\\CraDomainChooserTest::testSupportsWithCraAccount": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\LetsEncrypt\\DomainChooser\\CraDomainChooserTest::testSupportsWithDifferentAccount": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\LetsEncrypt\\DomainChooser\\CraDomainChooserTest::testChooseWithMissingCraCname": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testInvalidFormats with data set #12": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testInvalidFormats with data set #15": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testInvalidFormats with data set #6": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testInvalidFormats with data set #14": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testInvalidFormats with data set #1": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testInvalidFormats with data set #11": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testInvalidFormats with data set #2": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testInvalidFormats with data set #4": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testInvalidFormats with data set #13": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testInvalidFormats with data set #5": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testInvalidFormats with data set #7": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testInvalidFormats with data set #0": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testInvalidFormats with data set #8": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testInvalidFormats with data set #10": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testInvalidFormats with data set #3": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testInvalidFormats with data set #9": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testInvalidConstraint": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #22": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #15": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #14": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #8": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #18": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #25": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #2": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #4": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #19": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #26": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #10": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #23": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #24": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #13": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #20": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #5": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #11": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #0": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #7": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #9": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #12": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #3": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #21": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #16": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #28": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #6": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #1": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #17": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #27": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #29": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testAcceptsNull": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\SslTest::testGettersAndSetters": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\QueryParameterFormatValidatorTest::testAcceptsNull": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\QueryParameterFormatValidatorTest::testInvalidConstraint": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\QueryParameterFormatValidatorTest::testValidValues with data set #5": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\QueryParameterFormatValidatorTest::testValidValues with data set #0": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\QueryParameterFormatValidatorTest::testValidValues with data set #1": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\QueryParameterFormatValidatorTest::testValidValues with data set #9": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\QueryParameterFormatValidatorTest::testValidValues with data set #4": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\QueryParameterFormatValidatorTest::testValidValues with data set #3": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\QueryParameterFormatValidatorTest::testValidValues with data set #10": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\QueryParameterFormatValidatorTest::testValidValues with data set #2": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\QueryParameterFormatValidatorTest::testValidValues with data set #7": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\QueryParameterFormatValidatorTest::testValidValues with data set #8": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\QueryParameterFormatValidatorTest::testValidValues with data set #6": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\QueryParameterFormatValidatorTest::testInvalidValues with data set #3": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\QueryParameterFormatValidatorTest::testInvalidValues with data set #0": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\QueryParameterFormatValidatorTest::testInvalidValues with data set #4": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\QueryParameterFormatValidatorTest::testInvalidValues with data set #5": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\QueryParameterFormatValidatorTest::testInvalidValues with data set #2": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\QueryParameterFormatValidatorTest::testInvalidValues with data set #1": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\IpTest::testGettersAndSetters": 0.004, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\HotlinkProtection\\RefererAddSchemaTest::test": 0.001, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\AddTest::testOk with data set \"data 4\"": 0.055, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\AddTest::testOk with data set \"data 1\"": 0.099, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\AddTest::testOk with data set \"data 2\"": 0.057, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\AddTest::testOk with data set \"empty 1\"": 0.053, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\AddTest::testOk with data set \"data 3\"": 0.058, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\AddTest::testFail with data set \"bad value 2\"": 0.084, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\AddTest::testFail with data set \"bad type 1\"": 0.035, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\AddTest::testFail with data set \"bad value 4\"": 0.087, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\AddTest::testFail with data set \"bad value 1\"": 0.035, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\AddTest::testFail with data set \"bad type 5\"": 0.037, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\AddTest::testFail with data set \"bad type 6\"": 0.035, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\AddTest::testFail with data set \"bad type 3\"": 0.035, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\AddTest::testFail with data set \"bad type 4\"": 0.033, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\AddTest::testFail with data set \"bad value 3\"": 0.036, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\AddTest::testFail with data set \"bad value 5\"": 0.245, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\OriginBasedir\\AddTest::testFail with data set \"bad type 2\"": 0.034, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ServerLastDownStatusTest::testInvalidReason": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ServerLastDownStatusTest::test": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\UniqueCnameValidatorTest::testWithMultipleExistingCnames": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\UniqueCnameValidatorTest::testWorksWithNoCnames": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\UniqueCnameValidatorTest::testWithNoExistingCnames": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\UniqueCnameValidatorTest::testAcceptsNull": 0.003, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\UniqueCnameValidatorTest::testInvalidConstraint": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\UniqueCnameValidatorTest::testWithOneExistingCname": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\UniqueCnameValidatorTest::testWorksWithNullAsCnames": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GetCertificateControllerTest::testMultipleResourcesWithSingleCertificate": 0.953, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GetCertificateControllerTest::testSingleResourceWithoutCertificate": 0.043, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GetCertificateControllerTest::testSingleResourceWithMultipleCertificatesWhenAssignedIsMissing": 0.425, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GetCertificateControllerTest::testMultipleResourcesWhenOnlyOneHasCertificate": 0.407, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GetCertificateControllerTest::testSingleResourceWithSingleCertificate": 0.053, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GetCertificateControllerTest::testSingleResourceWithMultipleHistoryCertificatesButNoneAssigned": 0.575, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GetCertificateControllerTest::testSingleResourceWithMultipleCertificatesWhenAssignedIsNotTheLast": 0.557, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GetCertificateControllerTest::testSingleResourceWithMultipleCertificates": 0.409, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GetCertificateControllerTest::testEmptyResourceIds": 0.221, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainOrIpValidatorTest::testValidDomain with data set #1": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainOrIpValidatorTest::testValidDomain with data set #0": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainOrIpValidatorTest::testValidDomain with data set #3": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainOrIpValidatorTest::testValidDomain with data set #2": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainOrIpValidatorTest::testValidDomain with data set #4": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainOrIpValidatorTest::testValidDomain with data set #5": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainOrIpValidatorTest::testInvalidConstraint": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainOrIpValidatorTest::testInvalidDomainsOrIps with data set #7": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainOrIpValidatorTest::testInvalidDomainsOrIps with data set #2": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainOrIpValidatorTest::testInvalidDomainsOrIps with data set #1": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainOrIpValidatorTest::testInvalidDomainsOrIps with data set #4": 0.002, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainOrIpValidatorTest::testInvalidDomainsOrIps with data set #3": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainOrIpValidatorTest::testInvalidDomainsOrIps with data set #6": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainOrIpValidatorTest::testInvalidDomainsOrIps with data set #5": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainOrIpValidatorTest::testInvalidDomainsOrIps with data set #0": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\DomainOrIpValidatorTest::testAcceptsNull": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Internal\\Resource\\Servers\\ResourceServersSchemaTest::test": 0.004, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Internal\\Server\\Status\\AllServersStatusSchemaTest::test": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\LetsEncrypt\\ResultTest::testInterfaceWorks": 0.004, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Groups\\GroupListEntryTest::testFunctionality": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Ip\\Application\\Controller\\StatusControllerTest::testWithNonExistentIp": 0.189, "Cdn77\\NxgApi\\Tests\\Functional\\Ip\\Application\\Controller\\StatusControllerTest::testWithExistingIp": 0.058, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Internal\\Server\\ListControllerTest::testWithNoServers": 0.029, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Internal\\Server\\ListControllerTest::testWithMultipleServers": 0.061, "Cdn77\\NxgApi\\Tests\\Unit\\Core\\Domain\\Value\\CidrTest::testValidCidrFormat with data set \"2400:cb00:2048:1::c629:d7b8/128\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Core\\Domain\\Value\\CidrTest::testValidCidrFormat with data set \"***********/32\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Core\\Domain\\Value\\CidrTest::testValidCidrFormat with data set \"10.0.0.0/8\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Core\\Domain\\Value\\CidrTest::testValidCidrFormat with data set \"::/0\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Core\\Domain\\Value\\CidrTest::testValidCidrFormat with data set \"0.0.0.0/32\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Core\\Domain\\Value\\CidrTest::testValidCidrFormat with data set \"***********/24\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Core\\Domain\\Value\\CidrTest::testValidCidrFormat with data set \"2001:db8::/48\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Core\\Domain\\Value\\CidrTest::testInvalidCidrFormat with data set \"invalid IP no mask\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Core\\Domain\\Value\\CidrTest::testInvalidCidrFormat with data set \"valid IPv6, invalid mask\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Core\\Domain\\Value\\CidrTest::testInvalidCidrFormat with data set \"invalid IP with mask\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Core\\Domain\\Value\\CidrTest::testInvalidCidrFormat with data set \"valid IPv4, invalid mask\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Internal\\Resource\\Servers\\ResourceDetailTest::test": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testOk with data set \"priority min\"": 0.062, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testFailWithMultipleMainPriority": 0.253, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testFailWithMissingMainPriority": 0.048, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testOkMultipleOrigins with data set \"min 1, some 2\"": 0.324, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testFailWhenPriorityMissingMultipleOrigins": 0.045, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testFailWithMultipleOriginsWithSamePriority": 0.051, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testFail with data set \"bad type 4\"": 0.224, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testFail with data set \"bad type 1\"": 0.067, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testFail with data set \"bad type 6\"": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testFail with data set \"bad type 5\"": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testFail with data set \"bad value 1\"": 0.238, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testFail with data set \"bad value 2\"": 0.245, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testFail with data set \"bad type 3\"": 0.228, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testFail with data set \"bad type 2\"": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testFail with data set \"bad type 0\"": 0.067, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testFailWhenPriorityMissing": 0.066, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testFailMultipleOrigins with data set \"bad type 1 - first bad, second ok\"": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testFailMultipleOrigins with data set \"bad type 1 - both bad\"": 0.067, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Priority\\AddTest::testFailMultipleOrigins with data set \"bad type 1 - first ok, second bad\"": 0.24, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFailWhenFollowRedirectMissing": 0.049, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"basedir -> bad type 2\"": 0.249, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"basedir -> bad value 1\"": 0.048, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"empty -> bad type 2\"": 0.055, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"basedir -> bad value 5\"": 0.051, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"basedir -> bad type 3\"": 0.05, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"empty -> bad type 4\"": 0.048, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"empty -> bad value 3\"": 0.05, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"basedir -> bad type 6\"": 0.294, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"empty -> bad type 1\"": 0.113, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"basedir -> bad type 4\"": 0.051, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"empty -> bad type 3\"": 0.102, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"empty -> bad type 6\"": 0.103, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"basedir -> bad value 4\"": 0.249, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"empty -> bad value 4\"": 0.062, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"basedir -> bad type 1\"": 0.1, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"empty -> bad type 5\"": 0.233, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"basedir -> bad type 5\"": 0.051, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"basedir -> bad value 3\"": 0.261, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"empty -> bad value 1\"": 0.236, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"empty -> bad value 2\"": 0.064, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFail with data set \"basedir -> bad value 2\"": 0.11, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFailMultipleOrigins with data set \"empty -> bad value - first bad, second ok \"": 0.054, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFailMultipleOrigins with data set \"empty -> bad type - first ok, second bad\"": 0.053, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFailMultipleOrigins with data set \"empty -> bad value - first ok, second bad \"": 0.05, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFailMultipleOrigins with data set \"empty -> bad type - first, second ok\"": 0.11, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testOkMultipleOrigins with data set \"empty -> basedir\"": 0.07, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testOkMultipleOrigins with data set \"basedir -> empty\"": 0.068, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testOkMultipleOrigins with data set \"basedir -> another basedir\"": 0.338, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testFailWhenFollowRedirectMissingMultipleOrigins": 0.055, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testOk with data set \"empty -> basedir 1\"": 0.062, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testOk with data set \"basedir -> empty\"": 0.066, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testOk with data set \"basedir -> other basedir 1\"": 0.315, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testOk with data set \"empty -> basedir 2\"": 0.062, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testOk with data set \"empty -> basedir 3\"": 0.06, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testOk with data set \"empty -> empty\"": 0.109, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Basedir\\EditTest::testOk with data set \"empty -> basedir 4\"": 0.06, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceRefererProtectionAddressTest::test": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\IpProtection\\EditTest::testIpProtectionWholeNetwork": 0.289, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\IpProtection\\EditTest::testInvalidIpProtectionNetwork": 0.054, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\IpProtection\\EditTest::testInvalidIpProtectionType": 0.072, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\IpProtection\\EditTest::testDisableIpProtection": 0.084, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\IpProtection\\EditTest::testIpProtectionChangeBlacklistToWhitelist": 0.086, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\IpProtection\\EditTest::testIpProtectionWithNoAddresses": 0.082, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Internal\\Server\\ServerListSchemaTest::test": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\SslFileTest::testGettersAndSetters": 0.003, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\CreateControllerTest::testSuccessfulResult": 0.433, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\CreateControllerTest::testEnqueuedResultWithValidationError": 0.123, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\CreateControllerTest::testRejectsResultForFinishedRequest": 0.185, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\CreateControllerTest::testEnqueuedResultWithValidationErrorAndDuplicateTask": 0.22, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\CreateControllerTest::testEmptyValues": 0.251, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\CreateControllerTest::testRejectsResultForUnknownRequest": 0.021, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\CreateControllerTest::testEnqueuedResultWithReachedLetsEncryptLimit": 1.44, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceGeoProtectionTest::test": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\IpProtection\\IpProtectionEditSchemaTest::test": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\IpProtection\\IpProtectionEditSchemaTest::testNull": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Dns\\Generator\\ResourceDetailTest::test": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Internal\\Server\\ServerPopLocationDetailTest::test": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SuspensionControllerTest::testSuspendAction": 0.406, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SuspensionControllerTest::testUnsuspendAction": 0.081, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SuspensionControllerTest::testSuspendActionWithSuspendedResource": 0.07, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SuspensionControllerTest::testUnsuspendActionWithUnsuspendedResource": 0.061, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Internal\\Pop\\ListControllerTest::testWithNoPops": 0.011, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Internal\\Pop\\ListControllerTest::testWithMultiplePopsWithGroupsAndServers": 0.5, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Internal\\Pop\\ListControllerTest::testWithMultiplePops": 0.036, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\CustomLocationTest::testGettersAndSetters": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Dns\\Generator\\SnapshotSchemaTest::test": 0.007, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\DetailControllerTest::testExistingResource": 0.029, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\DetailControllerTest::testWithIgnoredQueryParams": 0.032, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\DetailControllerTest::testNonExistentResource": 0.268, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\Ssl\\CertificatesSchemaTest::test": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceIpProtectionAddressTest::test": 0.002, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\HotlinkProtection\\RefererEditSchemaTest::test": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\HotlinkProtection\\RefererEditSchemaTest::testWithNull": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Groups\\GroupListTest::testFunctionality": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EnableDatacentersControllerTest::testEnableLocations": 0.507, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\DomainLookupControllerTest::testLookupByCdnUrl": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\DomainLookupControllerTest::testWithMalformedDomain": 0.014, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\DomainLookupControllerTest::testWithNotFoundDomain": 0.027, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\DomainLookupControllerTest::testLookupByCname": 0.219, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ServerCurrentStatusTest::test": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ServerCurrentStatusTest::testInvalidReason": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFailWhenSslVerifyDisableMissingMultipleOrigins": 0.055, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testOkMultipleOrigins with data set \"true -> first true, second false\"": 0.069, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testOkMultipleOrigins with data set \"true -> first false, second true\"": 0.07, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testOkMultipleOrigins with data set \"false -> both true\"": 0.109, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFailWhenSslVerifyDisableMissing": 0.05, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 7\"": 0.047, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 4\"": 0.051, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 9\"": 0.09, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 8\"": 0.049, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 3\"": 0.047, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 6\"": 0.229, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 6\"": 0.053, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 2\"": 0.231, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 1\"": 0.091, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 8\"": 0.287, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 4\"": 0.056, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 3\"": 0.051, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 2\"": 0.052, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 9\"": 0.051, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 7\"": 0.086, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 5\"": 0.052, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"false -> bad enabled 1\"": 0.052, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFail with data set \"true -> bad enabled 5\"": 0.055, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFailMultipleOrigins with data set \"false -> bad first, ok second\"": 0.054, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFailMultipleOrigins with data set \"false -> ok first, bad second\"": 0.086, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFailMultipleOrigins with data set \"true -> bad first, ok second\"": 0.24, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testFailMultipleOrigins with data set \"true -> ok first, bad second\"": 0.06, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testOk with data set \"false -> true\"": 0.304, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testOk with data set \"false -> false\"": 0.082, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testOk with data set \"true -> true\"": 0.253, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\SslVerifyDisable\\EditTest::testOk with data set \"true -> false\"": 0.095, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ServerTest::testGettersAndSetters": 0.002, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\HotlinkProtection\\ResourceDetailTest::test": 0.006, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\IpProtection\\IpProtectionAddressDetailTest::test": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\DTO\\Legacy\\IpServerStatusDTOTest::testValues": 0.004, "Cdn77\\NxgApi\\Tests\\Functional\\Core\\Application\\Controller\\DocsControllerTest::testGetOpenApiDocsJson": 0.04, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Internal\\Server\\Status\\ServerStatusSchemaTest::testInvalidForced": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Internal\\Server\\Status\\ServerStatusSchemaTest::test": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Internal\\Server\\Force\\UnforceControllerTest::testUnforceAction": 0.028, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Internal\\Server\\Force\\UnforceControllerTest::testUnforceActionWithForcedUpServer": 0.036, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Internal\\Server\\Force\\UnforceControllerTest::testUnforceActionUpdatesExistingStatus": 0.272, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Internal\\Server\\Force\\UnforceControllerTest::testUnforceActionWithNonExistentServer": 0.011, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Groups\\ListControllerTest::testWorksWithNoGroups": 0.016, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Groups\\ListControllerTest::testWorksWithExistingGroups": 0.289, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\Legacy\\Edit\\Constraints\\UniqueCnamesValidatorTest::testInvalidCombinations with data set #2": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\Legacy\\Edit\\Constraints\\UniqueCnamesValidatorTest::testInvalidCombinations with data set #1": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\Legacy\\Edit\\Constraints\\UniqueCnamesValidatorTest::testInvalidCombinations with data set #0": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\Legacy\\Edit\\Constraints\\UniqueCnamesValidatorTest::testValidCombinations with data set #0": 0.002, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\Legacy\\Edit\\Constraints\\UniqueCnamesValidatorTest::testValidCombinations with data set #1": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\IpProtection\\IpProtectionAddSchemaTest::test": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\Legacy\\Certificate\\CertificateMetadataTest::testWithAllValuesSet": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\Legacy\\Certificate\\CertificateMetadataTest::testWithNullableValues": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Repository\\CdnResourceRepositoryTest::testFindForPermanentRemove": 0.042, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\Ssl\\CertificatesCertificateItemTest::test": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Internal\\Server\\ServerPopDetailTest::test": 0.001, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testFailWhenPortMissing": 0.039, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testOk with data set \"port min\"": 0.06, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testOk with data set \"default\"": 0.132, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testOk with data set \"some port\"": 0.061, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testOk with data set \"port max\"": 0.059, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testFailMultipleOrigins with data set \"bad type 1 - both bad\"": 0.112, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testFailMultipleOrigins with data set \"bad type 1 - first bad, second ok\"": 0.042, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testFailMultipleOrigins with data set \"bad type 1 - first ok, second bad\"": 0.043, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testOkMultipleOrigins with data set \"default both\"": 0.135, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testOkMultipleOrigins with data set \"value 1, default 2\"": 0.061, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testOkMultipleOrigins with data set \"default 1, value 2\"": 0.061, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testOkMultipleOrigins with data set \"value 1, value 2\"": 0.063, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testFail with data set \"bad type 6\"": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testFail with data set \"bad value 2\"": 0.309, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testFail with data set \"bad type 4\"": 0.247, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testFail with data set \"bad type 2\"": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testFail with data set \"bad type 1\"": 0.234, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testFail with data set \"bad type 3\"": 0.228, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testFail with data set \"bad type 5\"": 0.118, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testFail with data set \"bad value 1\"": 0.042, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testFail with data set \"bad type 0\"": 0.043, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\Port\\AddTest::testFailWhenPortMissingMultipleOrigins": 0.042, "Cdn77\\NxgApi\\Tests\\Unit\\DTO\\NxgConfGen\\ServerDTOTest::testInterfaceWorks": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Dns\\Generator\\LocationDetailTest::test": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Resource\\Domain\\ResourceDatacentersEnablerTest::testEnableNoLocations": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Resource\\Domain\\ResourceDatacentersEnablerTest::testEnableLocations": 0.004, "Cdn77\\NxgApi\\Tests\\Functional\\Server\\Domain\\ServerPauserTest::testPause": 0.022, "Cdn77\\NxgApi\\Tests\\Functional\\Server\\Domain\\ServerPauserTest::testUnpause": 0.025, "Cdn77\\NxgApi\\Tests\\Functional\\Server\\Domain\\ServerPauserTest::testUnpauseWithUnpausedServer": 0.017, "Cdn77\\NxgApi\\Tests\\Functional\\Server\\Domain\\ServerPauserTest::testPauseWithPausedServer": 0.019, "Cdn77\\NxgApi\\Tests\\Unit\\DTO\\Legacy\\ServerUpIpListDTOTest::testInterfaceWorks": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testValidFormat with data set \"**********/32\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testValidFormat with data set \"0.0.0.0/0\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testValidFormat with data set \"*******/30\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testValidFormat with data set \"***********/32\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testValidFormat with data set \"********/26\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testValidFormat with data set \"*******/32\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testValidFormat with data set \"2a02:6ea0:dc0d::1335/128\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testValidFormat with data set \"*********/8\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testValidFormat with data set \"2a02:6ea0:dc0d::/64\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testValidFormat with data set \"2000::/8\"": 0.006, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testValidFormat with data set \"*********/25\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testRejectsInvalidType": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testInvalidFormat with data set \"invalid address part\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testInvalidFormat with data set \"incorrect ip part format #2\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testInvalidFormat with data set \"incorrect ip part format\"": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testInvalidFormat with data set \"bits outside of mask\"": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testInvalidFormat with data set \"range\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testInvalidFormat with data set \"incorrect ip part format #3\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testInvalidFormat with data set \"list\"": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testInvalidFormat with data set \"invalid mask part\"": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testInvalidFormat with data set \"invalid ipv6 mask 129\"": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testInvalidFormat": 0.006, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testInvalidFormat with data set \"nonip format\"": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testAllowsNull": 0.021, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Ip\\CidrValidatorTest::testRejectsInvalidConstraint": 0.002, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\LocationTest::testGettersAndSetters": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Dns\\Generator\\PopDetailTest::test": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\LetsEncrypt\\RenewalManagerTest::testEnqueueOldCertificatesForRenewal": 0.145, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\LetsEncrypt\\RenewalManagerTest::testEnqueueCertificatesRenewalForSpecificResources": 0.07, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\LetsEncrypt\\RenewalManagerTest::testEnqueueCertificatesRenewalForSpecificResourcesAsap": 0.055, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\EditTest::testSingleOrigin": 0.057, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\EditTest::testPartialEdit": 0.001, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\EditTest::testLegacy": 0.052, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\EditTest::testDefaultValuesForOrigin": 0.057, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\EditTest::testMultipleOrigins": 0.063, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\EditTest::testOriginsHavePriorityOverLegacy": 0.134, "Cdn77\\NxgApi\\Tests\\Functional\\Server\\Application\\Controller\\AllStatusControllerTest::testWithMultipleServers": 0.241, "Cdn77\\NxgApi\\Tests\\Functional\\Server\\Application\\Controller\\AllStatusControllerTest::testWithNoServers": 0.242, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFailWhenFollowRedirectMissing": 0.042, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad codes 2\"": 0.038, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad codes 3\"": 0.021, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad codes 7\"": 0.224, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 8\"": 0.829, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 1\"": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad codes 5\"": 0.031, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 2\"": 0.035, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad codes 12\"": 0.034, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad codes 6\"": 0.034, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad codes 10\"": 0.042, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 3\"": 0.047, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad codes 9\"": 0.048, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad combination 1\"": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 11\"": 0.251, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad codes 13\"": 0.065, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 10\"": 0.246, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 12\"": 0.033, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 7\"": 0.034, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad codes 1\"": 0.018, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 13\"": 0.329, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad codes 11\"": 0.043, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad combination 2\"": 0.033, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 14\"": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad codes 4\"": 0.018, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad codes 8\"": 0.042, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 5\"": 0.033, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 6\"": 0.236, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 9\"": 0.146, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testFail with data set \"bad enabled 4\"": 0.034, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testOk with data set \"data 1\"": 0.296, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testOk with data set \"empty 2\"": 0.055, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testOk with data set \"data 2\"": 0.057, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testOk with data set \"empty 1\"": 0.071, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Certificate\\CertificatePairValidatorTest::testValidCertificatePair with data set #0": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Certificate\\CertificatePairValidatorTest::testInvalidCertificatePair with data set #0": 0.001, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\Ssl\\SetControllerTest::testWithExistingSslFilesAndMissingSsl": 0.215, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\Ssl\\SetControllerTest::testWithInvalidCertificatePair": 0.535, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\Ssl\\SetControllerTest::testWithMultipleCertificateConfigurations with data set #2": 0.134, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\Ssl\\SetControllerTest::testWithMultipleCertificateConfigurations with data set #0": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\Ssl\\SetControllerTest::testWithMultipleCertificateConfigurations with data set #4": 0.101, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\Ssl\\SetControllerTest::testWithMultipleCertificateConfigurations with data set #3": 0.058, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\Ssl\\SetControllerTest::testWithMultipleCertificateConfigurations with data set #1": 0.237, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\Ssl\\SetControllerTest::testWithNoPreviousCertificateSet": 0.321, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\Ssl\\SetControllerTest::testWithInvalidCertificateChain": 0.447, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Legacy\\Resources\\Ssl\\SetControllerTest::testWithExistingPreviousCertificateFiles": 0.226, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Dns\\Generator\\ServerIpDetailTest::test": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ServersStatusControllerTest::testWithInvalidResource": 0.019, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ServersStatusControllerTest::testWithCustomLocation": 0.059, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ServersStatusControllerTest::testWithCustomLocationWithNotMatchingGroups": 0.128, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ServersStatusControllerTest::testWithExistingServers": 0.142, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ServersStatusControllerTest::testWithNoServersInGroup": 0.048, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testSslIsRemovedWhenNoCustomCnamesAreGiven": 0.132, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testCertificateRequestIsCreatedWhenCnamesAreRemovedForInstantSsl": 0.132, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testEditResourceWithExistingPreviousCertificateFiles": 0.16, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWithInvalidCertificatePair": 0.502, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWhenInstantSslIsDisabledWithSslCertificateAndKeySetToNull with data set #0": 0.277, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWhenInstantSslIsDisabledWithSslCertificateAndKeySetToNull with data set #2": 0.132, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWhenInstantSslIsDisabledWithSslCertificateAndKeySetToNull with data set #1": 0.073, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testCertificateRequestIsNotCreatedWhenNoCnamesAreBeingEditedForInstantSsl": 0.318, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditRemoveSslWithSslNotSet": 0.092, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testExistingCertificateIsUntouchedAndSslNotRemovedWhenInstantSslDisabledAndNoCnamesAreGiven": 0.324, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditChangeCustomCertificateToInstantSslWithEmptySslField with data set #1": 0.091, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditChangeCustomCertificateToInstantSslWithEmptySslField with data set #2": 0.139, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditChangeCustomCertificateToInstantSslWithEmptySslField with data set #0": 0.078, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testCertificateRequestIsNotCreatedWhenNoCnamesAreGiven": 0.071, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testEditResourceSslWithNoPreviousCertificateSet": 0.487, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditFailWhenInstantSslIsEnabledWithSslCertificateSetup": 0.392, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWhereSslFilesAreLeftIntactWhenSslIsDeleted": 0.092, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testCertificateRequestIsCancelledWhenCnamesAreRemoved": 0.084, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWithInvalidCertificateChain": 0.618, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testInstantSslEnabled": 0.077, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditFailWhenInstantSslSettingsAreMissingForSslSetup": 0.142, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditRemoveExistingSsl": 0.075, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testCertificateRequestIsCancelledWhenInstantSslIsDisabled": 0.135, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testCertificateRequestIsNotCreatedAndSslRemovedWhenNoCustomCnamesAreGiven": 0.081, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testCertificateRequestIsCreatedAndSslNotRemovedWhenCnamesAreAddedForInstantSsl": 0.136, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testCertificateRequestIsNotCreatedWhenCnamesAreChangedForInstantSslDisabled": 0.071, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testCertificateRequestIsCreatedForCraWithOneDomain": 0.076, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWithMultipleCertificateConfigurations with data set #3": 0.118, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWithMultipleCertificateConfigurations with data set #1": 0.084, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWithMultipleCertificateConfigurations with data set #0": 0.302, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWithMultipleCertificateConfigurations with data set #4": 0.32, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWithMultipleCertificateConfigurations with data set #2": 0.085, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testInstantSslRemainsEnabled with data set #0": 0.076, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testInstantSslRemainsEnabled with data set #2": 0.081, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testInstantSslRemainsEnabled with data set #1": 0.075, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWithExistingSslFilesAndMissingSsl": 0.674, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testCertificateRequestIsCreatedForCraWithMultipleDomains": 0.144, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\UniqueSetValidatorTest::testInvalidConstraint": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\UniqueSetValidatorTest::testInvalid with data set #3": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\UniqueSetValidatorTest::testInvalid with data set #0": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\UniqueSetValidatorTest::testInvalid with data set #1": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\UniqueSetValidatorTest::testInvalid with data set #4": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\UniqueSetValidatorTest::testInvalid with data set #2": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\UniqueSetValidatorTest::testAcceptsNull": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\UniqueSetValidatorTest::testValid with data set #4": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\UniqueSetValidatorTest::testValid with data set #3": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\UniqueSetValidatorTest::testValid with data set #1": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\UniqueSetValidatorTest::testValid with data set #2": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\UniqueSetValidatorTest::testValid with data set #0": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\UniqueSetValidatorTest::testEmptyArray": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\ResourceListTest::testFunctionality": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Internal\\Server\\Force\\ForceDownControllerTest::testForceDownActionWithForcedDownServer": 0.072, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Internal\\Server\\Force\\ForceDownControllerTest::testForceDownAction": 0.279, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Internal\\Server\\Force\\ForceDownControllerTest::testForceDownActionWithNonExistentServer": 0.012, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Internal\\Server\\Force\\ForceDownControllerTest::testForceDownActionUpdatesExistingStatus": 0.075, "Cdn77\\NxgApi\\Tests\\Functional\\FullLogs\\Application\\Controller\\StatusControllerTest::testStatusDisabled": 0.035, "Cdn77\\NxgApi\\Tests\\Functional\\FullLogs\\Application\\Controller\\StatusControllerTest::testStatusEnabled": 0.103, "Cdn77\\NxgApi\\Tests\\Functional\\FullLogs\\Application\\Controller\\StatusControllerTest::testResourceNotFound": 0.02, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GeoLocationProtection\\EditTest::testInvalidGeoProtectionType": 0.28, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GeoLocationProtection\\EditTest::testNoGeoLocations": 0.114, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GeoLocationProtection\\EditTest::testInvalidCountryCode": 0.098, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GeoLocationProtection\\EditTest::testGeoProtectionChangeBlacklistToWhitelist": 0.111, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GeoLocationProtection\\EditTest::testDisableGeoLocations": 0.102, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Payload\\ResourceAddSchemaTest::testDeserialization": 0.004, "Cdn77\\NxgApi\\Tests\\Functional\\Server\\Application\\Controller\\ServerIdListControllerTest::testBadRequest": 0.014, "Cdn77\\NxgApi\\Tests\\Functional\\Server\\Application\\Controller\\ServerIdListControllerTest::testListResponseForResourceIds": 0.308, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Internal\\Server\\ServerIpAddressesDetailTest::testNullable": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Internal\\Server\\ServerIpAddressesDetailTest::test": 0, "Cdn77\\NxgApi\\Tests\\Unit\\DTO\\Internal\\Server\\Status\\ServerWithPrimaryIpPairDTOTest::test": 0.001, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\HotlinkProtection\\EditTest::testHotlinkProtectionWithInvalidAddress": 0.043, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\HotlinkProtection\\EditTest::testHotlinkProtectionWithNoAddresses": 0.064, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\HotlinkProtection\\EditTest::testHotlinkProtectionChangeBlacklistToWhitelist": 0.067, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\HotlinkProtection\\EditTest::testDisableHotlinkProtectionWithEmptyDenied": 0.057, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\HotlinkProtection\\EditTest::testHotlinkProtectionWithTooManyAddresses": 0.061, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\HotlinkProtection\\EditTest::testDisableHotlinkProtection": 0.157, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\HotlinkProtection\\EditTest::testInvalidHotlinkProtectionType": 0.103, "Cdn77\\NxgApi\\Tests\\Unit\\Resource\\Domain\\Factory\\SslFileFactoryTest::testCreateForSsl": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\LocationCoordinateTest::testGettersAndSetters": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\ResourceEditSchemaTest::testFunctionality": 0.003, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\Messaging\\Message\\ResourceChangeMessageTest::testCorrect": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\Messaging\\Message\\ResourceChangeMessageTest::testInvalidType": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Certificate\\PrivateKeyValidatorTest::testInvalidPrivateKey with data set #0": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Certificate\\PrivateKeyValidatorTest::testInvalidPrivateKey with data set #2": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Certificate\\PrivateKeyValidatorTest::testInvalidPrivateKey with data set #1": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Certificate\\PrivateKeyValidatorTest::testEmptyPrivateKey with data set #0": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Certificate\\PrivateKeyValidatorTest::testValidPrivateKey with data set #0": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Server\\Application\\Payload\\ResourceIdsSchemaTest::testDeserialize": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\IgnoredQueryParamTest::test": 0, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testWithSuspendedTooLongResource": 0.028, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testMultipleOriginsWithFailoverOriginPropertiesSameAsResource": 0.048, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testMultipleOrigins": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testFollowRedirect with data set \"all set 1\"": 0.036, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testFollowRedirect with data set \"disabled\"": 0.209, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testFollowRedirect with data set \"all set 2\"": 0.255, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testFollowRedirect with data set \"only enabled\"": 0.071, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testIgnoreQueryParams": 0.034, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testWithInvalidResource": 0.016, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::test": 0.068, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testSecureToken with data set \"all set 1\"": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testSecureToken with data set \"disabled\"": 0.037, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testSecureToken with data set \"some set 1\"": 0.039, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissingMultipleOrigins with data set \"empty 1\"": 0.289, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissingMultipleOrigins with data set \"data 1\"": 0.058, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissingMultipleOrigins with data set \"data 2\"": 0.056, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissingMultipleOrigins with data set \"empty 2\"": 0.055, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailMultiple with data set \"not used -> first ok, second bad codes\"": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailMultiple with data set \"not used -> first bad, second ok\"": 0.081, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailMultiple with data set \"not used -> first ok, second bad\"": 0.28, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailMultiple with data set \"not used -> first bad codes, second ok\"": 0.039, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOkMultipleOrigins with data set \"enabled with codes -> first off, second changed\"": 0.081, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOkMultipleOrigins with data set \"enabled with codes -> enabled with changed codes\"": 0.128, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissing with data set \"data 1\"": 0.051, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissing with data set \"empty 1\"": 0.05, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissing with data set \"empty 2\"": 0.054, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissing with data set \"data 2\"": 0.057, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOk with data set \"enabled with codes -> only enabled\"": 0.108, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOk with data set \"not used -> turn on enabled with codes 1\"": 0.11, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOk with data set \"only enabled -> enabled with codes 2\"": 0.439, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOk with data set \"not used -> turn on enabled with codes 2\"": 0.143, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOk with data set \"enabled with codes -> off\"": 0.07, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOk with data set \"only enabled -> enabled with codes 1\"": 0.139, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOk with data set \"not used -> off\"": 0.121, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOk with data set \"only enabled -> only enabled\"": 0.12, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOk with data set \"not used -> turn on enabled\"": 0.086, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOk with data set \"enabled with codes -> enabled with changed codes\"": 0.147, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testOk with data set \"only enabled -> off\"": 0.124, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 1\"": 0.115, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 7\"": 0.066, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 11\"": 0.096, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 10\"": 0.088, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 3\"": 0.048, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 2\"": 0.059, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 9\"": 0.084, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"origin with codes -> bad combination 1\"": 0.353, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 4\"": 0.257, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 4\"": 0.059, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 8\"": 0.063, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 11\"": 0.323, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 7\"": 0.348, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 6\"": 0.069, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 8\"": 0.067, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 7\"": 0.092, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 4\"": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 4\"": 0.133, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 2\"": 0.054, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 2\"": 0.034, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 3\"": 0.055, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 11\"": 0.052, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 7\"": 0.055, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 5\"": 0.074, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 9\"": 0.065, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 9\"": 0.292, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 12\"": 0.087, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 3\"": 0.266, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 4\"": 0.068, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 2\"": 0.086, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 1\"": 0.038, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 5\"": 0.042, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 6\"": 0.288, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 5\"": 0.314, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 7\"": 0.059, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 6\"": 0.643, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 7\"": 0.067, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 1\"": 0.073, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 3\"": 0.113, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 2\"": 0.035, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 10\"": 0.081, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 5\"": 0.06, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 10\"": 0.094, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 9\"": 0.057, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 5\"": 0.227, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad combination 1\"": 0.056, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 6\"": 0.062, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad combination 1\"": 1.279, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 1\"": 0.047, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 3\"": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad codes 6\"": 0.264, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 12\"": 0.083, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 9\"": 0.061, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad codes 1\"": 0.284, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 5\"": 0.27, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 6\"": 0.094, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 4\"": 0.057, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"enabled with codes -> bad enabled 2\"": 0.055, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad codes 12\"": 0.308, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 8\"": 0.079, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 9\"": 0.066, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad enabled 3\"": 0.075, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\FollowRedirect\\EditTest::testFail with data set \"only enabled -> bad enabled 1\"": 0.071, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\LetsEncrypt\\Queue\\RequestsWithDomainsListSchemaTest::testInterfaceWorks": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\DatacenterListControllerTest::testCustomDcLocationsListResponseForResource": 0.136, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\DatacenterListControllerTest::testNonCustomDcLocationsListResponseForResource": 0.104, "Cdn77\\NxgApi\\Tests\\Unit\\IpProtection\\Domain\\SetupIpProtectionTest::testEnable": 0, "Cdn77\\NxgApi\\Tests\\Unit\\IpProtection\\Domain\\SetupIpProtectionTest::testDisable": 0.002, "Cdn77\\NxgApi\\Tests\\Unit\\IpProtection\\Domain\\SetupIpProtectionTest::testUpdate": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\ExistsValidatorTest::testInvalidConstraint": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\ExistsValidatorTest::testInvalidResource": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\ExistsValidatorTest::testAcceptsNull": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\ExistsValidatorTest::testExistingResource": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Internal\\Server\\DetailControllerTest::testWithMultipleIpAddresses": 0.604, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Internal\\Server\\DetailControllerTest::testWithNoIp": 0.064, "Cdn77\\NxgApi\\Tests\\Functional\\Controller\\Internal\\Server\\DetailControllerTest::testWithNonExistentServer": 0.023, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\LetsEncrypt\\DomainChooser\\DomainChooserChainTest::testGetOrderedChoosers": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\LetsEncrypt\\DomainChooser\\DomainChooserChainTest::testSupports": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\LetsEncrypt\\DomainChooser\\DomainChooserChainTest::testAdd": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\LetsEncrypt\\DomainChooser\\DomainChooserChainTest::testChooseWithPriorities": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\LetsEncrypt\\DomainChooser\\DomainChooserChainTest::testChoose": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Dns\\Generator\\ServerLastDownDetailTest::test": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Dns\\Generator\\ServerDetailTest::test with data set #0": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Dns\\Generator\\ServerDetailTest::test with data set #1": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Legacy\\Resources\\IpProtection\\IpProtectionDetailSchemaTest::test": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testInvalidFormats with data set #10": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testInvalidFormats with data set #3": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testInvalidFormats with data set #2": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testInvalidFormats with data set #6": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testInvalidFormats with data set #11": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testInvalidFormats with data set #8": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testInvalidFormats with data set #1": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testInvalidFormats with data set #9": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testInvalidFormats with data set #7": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testInvalidFormats with data set #4": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testInvalidFormats with data set #12": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testInvalidFormats with data set #5": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testInvalidFormats with data set #0": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testValidFormats with data set #0": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testValidFormats with data set #9": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testValidFormats with data set #15": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testValidFormats with data set #4": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testValidFormats with data set #3": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testValidFormats with data set #14": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testValidFormats with data set #18": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testValidFormats with data set #13": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testValidFormats with data set #6": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testValidFormats with data set #11": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testValidFormats with data set #2": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testValidFormats with data set #5": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testValidFormats with data set #12": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testValidFormats with data set #10": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testValidFormats with data set #8": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testValidFormats with data set #19": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testValidFormats with data set #17": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testValidFormats with data set #7": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testValidFormats with data set #16": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testValidFormats with data set #1": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testInvalidConstraint": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\OriginUrlValidatorTest::testAcceptsNull": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\LetsEncrypt\\Queue\\RequestDomainsSchemaTest::testInterfaceWorks": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\LetsEncrypt\\Queue\\RequestDomainsSchemaTest::testRejectsEmptyDomains": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Schema\\Dns\\Generator\\LocationGroupDetailTest::test": 0, "Cdn77\\NxgApi\\Tests\\Unit\\DTO\\Internal\\Server\\Certificates\\ServerCertificateStatusDTOTest::test": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\PopTest::testGettersAndSetters": 0.002, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\Legacy\\Certificate\\CertificateStatusTest::testWIthNullableValues": 0.002, "Cdn77\\NxgApi\\Tests\\Unit\\Service\\Legacy\\Certificate\\CertificateStatusTest::testWithAllValuesSet": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\LetsEncrypt\\TaskTest::testInterfaceWorks": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\LetsEncrypt\\RequestManagerTest::testCancelAllPendingRequests": 0.102, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\LetsEncrypt\\RequestManagerTest::testEnqueue": 0.025, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\LetsEncrypt\\RequestManagerTest::testClearQueue": 0.03, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\LetsEncrypt\\RequestManagerTest::testCreateAndScheduleRequest": 0.026, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceTest::testAddCname": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceTest::testAccount": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceTest::testInitialTime": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceTest::testCnamesAreCaseInsensitive": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceTest::testGetCustomAndSharedCnames with data set #2": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceTest::testGetCustomAndSharedCnames with data set #5": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceTest::testGetCustomAndSharedCnames with data set #6": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceTest::testGetCustomAndSharedCnames with data set #4": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceTest::testGetCustomAndSharedCnames with data set #0": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceTest::testGetCustomAndSharedCnames with data set #3": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceTest::testGetCustomAndSharedCnames with data set #1": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceTest::testgetCnamesWithNoCname": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceTest::testGettersAndSetters": 0.005, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceTest::testHasCustomAndSharedCnames with data set #6": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceTest::testHasCustomAndSharedCnames with data set #2": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceTest::testHasCustomAndSharedCnames with data set #3": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceTest::testHasCustomAndSharedCnames with data set #4": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceTest::testHasCustomAndSharedCnames with data set #0": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceTest::testHasCustomAndSharedCnames with data set #5": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Entity\\Legacy\\ResourceTest::testHasCustomAndSharedCnames with data set #1": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testOk with data set \"empty 1\"": 0.062, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testOk with data set \"data 1\"": 0.066, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Invalid name with colon\"": 0.255, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Invalid value with backslash\"": 0.065, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"bad type 0\"": 0.324, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"bad type 5\"": 0.021, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"bad type 4\"": 0.93, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name\"": 0.241, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"bad type 1\"": 0.625, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"bad type 2\"": 0.022, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"bad type 6\"": 0.286, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"bad type 3\"": 0.023, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFailWhenResponseHeadersMissing": 0.053, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFailWhenResponseHeadersMissing": 0.244, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testOk with data set \"some header -> another header\"": 0.06, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testOk with data set \"empty -> empty\"": 0.085, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testOk with data set \"empty -> some header\"": 0.061, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testOk with data set \"some header -> empty\"": 0.065, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"bad type 4\"": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"bad type 0\"": 0.27, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"Forbidden header name\"": 0.06, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"bad type 1\"": 0.039, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"bad type 3\"": 2.958, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"Invalid name with colon\"": 0.046, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"Invalid value with backslash\"": 0.072, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"bad type 6\"": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"bad type 5\"": 0.271, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"bad type 2\"": 0.267, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Domain\\Finder\\ResourcesForPermanentRemoveFinderTest::testSuspend": 0.083, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Domain\\Finder\\ResourcesForPermanentRemoveFinderTest::test": 2.239, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testOk": 0.508, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testWithOnlyOneAssignedCertificate": 0.124, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testWithOneAssignedAndOneBackupCertificate": 0.234, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testWithSslWithoutFiles": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testMultipleResources": 0.431, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testWithAssignedIndexLowerThanMaxIndex": 0.159, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testImportantAccountExcluded": 0.398, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\Legacy\\Delete\\ResourceDeleterTest::testEnqueueOldCertificatesForRenewal": 0.723, "Cdn77\\NxgApi\\Tests\\Functional\\Core\\Application\\GraphQL\\GraphQLClientTest::testWorks": 1.444, "Warning": 0.009, "Cdn77\\NxgApi\\Tests\\Unit\\Clap\\AccountFinderTest::testFindVipAndTopReturnsCorrectIds": 5.129, "Cdn77\\NxgApi\\Tests\\Unit\\Clap\\AccountFinderTest::testOnlyVip": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Clap\\AccountFinderTest::testBothTypesSet": 0.003, "Cdn77\\NxgApi\\Tests\\Unit\\Clap\\AccountFinderTest::testMinAccounts": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Clap\\AccountFinderTest::testMinimumAccounts": 0.001, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testLimit": 0.279, "Cdn77\\NxgApi\\Tests\\Unit\\Log\\Formatter\\RequestFormatterTest::testLengthLimit": 0.008, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testInvalidValues with data set #13": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testInvalidValues with data set #14": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\Legacy\\Delete\\ResourceDeleterTest::testOk": 0.107, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testInvalidValues with data set #14": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedOriginDomainValidatorTest::testInvalidValues with data set #13": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFailType with data set \"bad type 6\"": 0.025, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFailType with data set \"bad type 2\"": 0.03, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFailType with data set \"bad type 3\"": 0.23, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFailType with data set \"bad type 4\"": 0.425, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFailType with data set \"bad type 1\"": 0.227, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFailType with data set \"bad type 0\"": 0.033, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFailType with data set \"bad type 5\"": 0.226, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"bad type\"": 0.272, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFailType with data set \"bad type 6\"": 0.21, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFailType with data set \"bad type 2\"": 0.059, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFailType with data set \"bad type 0\"": 0.034, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFailType with data set \"bad type 3\"": 0.216, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFailType with data set \"bad type 1\"": 0.034, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFailType with data set \"bad type 5\"": 0.205, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\GeoLocationProtection\\EditTest::testAllowedNotIsoCountryCode": 0.21, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFailWhenCorsIsEnabled": 0.273, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFailWhenCorsIsEnabled": 0.05, "Error": 0.001, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 11\"": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 0\"": 0.061, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 3\"": 0.059, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 5\"": 0.342, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 4\"": 0.051, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 10\"": 0.294, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 1\"": 0.049, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 7\"": 0.039, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 8\"": 0.269, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 9\"": 0.035, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden prefix\"": 0.058, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 2\"": 0.055, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testFail with data set \"Forbidden header name 6\"": 0.042, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testOk with data set \"data 2\"": 0.416, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\AddTest::testOk with data set \"x-77 in middle\"": 0.09, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"Forbidden header name 7\"": 0.046, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"Forbidden header name 8\"": 0.263, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"Forbidden header name 3\"": 0.279, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"Forbidden header name 5\"": 0.074, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"Forbidden header name 0\"": 0.045, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"Forbidden header name 2\"": 0.259, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"Forbidden header name 10\"": 0.253, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"Forbidden header name 9\"": 0.046, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"Forbidden header name 1\"": 0.242, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"Forbidden header name 6\"": 0.069, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"Forbidden header name 11\"": 0.049, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"Forbidden header name 4\"": 0.266, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\ResponseHeaders\\EditTest::testFail with data set \"Forbidden header name 12\"": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\AddTest::testOk with data set \"disabled 1\"": 0.44, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testOk with data set \"disabled 1\"": 0.57, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testOk with data set \"disabled\"": 0.064, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testOk with data set \"all files\"": 0.057, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testOk with data set \"mp4\"": 0.319, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad mode 1\"": 0.288, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"not used -> turn on all files\"": 0.053, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad mode 2\"": 0.037, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad mode 4\"": 0.035, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad mode 3\"": 0.052, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad mode 5\"": 0.033, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad mode 6\"": 0.031, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad mode 7\"": 0.019, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad mode 8\"": 0.016, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size 5\"": 0.044, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size 2\"": 0.257, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad combination 2\"": 0.033, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size 3\"": 0.039, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size 4\"": 0.033, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad combination 1\"": 0.037, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size 6\"": 0.215, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size 1\"": 0.033, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFailWhenFollowRedirectMissing": 0.03, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size 7\"": 0.032, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size 8\"": 0.392, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size 9\"": 0.03, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"not used -> turn on mp4\"": 0.304, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"mp4 -> disabled\"": 0.049, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"all files -> disabled\"": 0.052, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"change size\"": 0.047, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testOk with data set \"change mode\"": 0.238, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad mode 1\"": 0.043, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"not used -> bad size 1\"": 0.038, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \" enabled -> bad disabled 1\"": 0.413, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"mp4_only -> bad combination 1\"": 0.036, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFailWhenFollowRedirectMissing": 0.615, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\FollowRedirect\\EditTest::testFail with data set \"mp4_only -> bad disabled 1\"": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testOk with data set \"change mode\"": 0.069, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testOk with data set \"mp4 -> disabled\"": 0.065, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testOk with data set \"all files -> disabled\"": 0.071, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testOk with data set \"not used -> turn on mp4\"": 0.072, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testOk with data set \"not used -> turn on all files\"": 0.122, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testOk with data set \"change size\"": 0.354, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testFail with data set \"mp4_only -> bad combination 1\"": 0.013, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testFail with data set \"not used -> bad size 1\"": 0.052, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testFail with data set \"not used -> bad combination 1\"": 0.05, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testFail with data set \"not used -> bad mode 1\"": 0.051, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testFail with data set \"mp4_only -> bad disabled 1\"": 0.116, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testFailWhenFollowRedirectMissing": 0.015, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testFail with data set \"mp4_only -> size out of range 2\"": 0.28, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testFail with data set \"mp4_only -> size out of range 1\"": 0.295, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size range 1\"": 0.259, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size range 2\"": 0.03, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testSlicing with data set \"disabled\"": 0.15, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testSlicing with data set \"all\"": 0.043, "Cdn77\\NxgApi\\Tests\\Functional\\NgxConfGen\\Application\\Controller\\ResourceControllerTest::testSlicing with data set \"mp4\"": 0.041, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\EditTest::testFailWhenSlicingMissing": 0.261, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Slicing\\AddTest::testFail with data set \"bad size range 3\"": 0.071, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1": 3.953, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1 with data set \"name 1\"": 0.151, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1 with data set \"name 3\"": 0.455, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1 with data set \"name 6\"": 0.13, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1 with data set \"name 5\"": 0.087, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1 with data set \"name 2\"": 0.106, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1 with data set \"name 4\"": 0.104, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"name 7\"": 0.095, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"name 2\"": 0.223, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"name 5\"": 0.072, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"name 8\"": 0.101, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"name 4\"": 0.33, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"name 1\"": 0.239, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"name 9\"": 0.172, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"name 6\"": 0.805, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"name 3\"": 0.247, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"name 10\"": 0.099, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"name 12\"": 0.211, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"name 11\"": 0.088, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"resource 1 variants 1\"": 0.174, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"resource 1 variants 4\"": 0.223, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"valid resource ID without any record in DB 1\"": 0.174, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"valid resource ID without any record in DB 2\"": 0.257, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"valid resource ID without any record in DB 3\"": 0.178, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"unknown name 3\"": 0.1, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"resource 1 variants 6\"": 0.245, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"unknown name 2\"": 0.4, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"resource 1 variants 3\"": 0.445, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"resource 1 variants 2\"": 0.098, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"unknown name 1\"": 0.134, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testIgnoringFilesForResource1AndUnknownFilenames with data set \"resource 1 variants 5\"": 0.263, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testArchiveCertificatesForIdInUsedIds": 0.09, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testArchiveOnlyResourcesInUsedIds": 0.44, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testDryRun": 0.286, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testError": 0.239, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testOnlyExpired": 0.396, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithId": 0.246, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #31": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testValidFormats with data set #30": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testInvalidFormats with data set #16": 0, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\Referer\\DomainFormatValidatorTest::testInvalidFormats with data set #17": 0.001, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testOkWithIdAndCdnUrl": 0.24, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testOnlyInactiveResources": 1.416, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testOnlyOneMoveOnlyOptionIsAllowed": 0.046, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testOnlyResourcesWithInactiveSsl": 0.444, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testWithFilesWithoutSsl": 0.299, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\CertificateCleanerTest::testWithOneFileWithoutSsl": 0.174, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testInvalidValues with data set #15": 0.001, "Cdn77\\NxgApi\\Tests\\Unit\\Validator\\Constraints\\Resource\\AllowedCnameDomainValidatorTest::testInvalidValues with data set #16": 0, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\ExternalApi\\CertificateBucket\\CertificateBucketTest::testGetLastMainCertificatePair": 6.044, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\ExternalApi\\CertificateBucket\\CertificateBucketTest::testGetLastMainCertificatePairIncomplete": 1.55, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\ExternalApi\\CertificateBucket\\CertificateBucketTest::testGetLastMainCertificatePairNoFiles": 7.427, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Domain\\Finder\\CertificateBucketMainCertificateTest::testGetLastMainCertificatePairNoFiles": 0.009, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Domain\\Finder\\CertificateBucketMainCertificateTest::testGetLastMainCertificatePair": 0.008, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Domain\\Finder\\CertificateBucketMainCertificateTest::testGetLastMainCertificatePairIncomplete": 0.135, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Domain\\Finder\\MainCertificateFinderTest::testGetLastMainCertificatePair": 0.009, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Domain\\Finder\\MainCertificateFinderTest::testGetLastMainCertificatePairIncomplete": 0.008, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Domain\\Finder\\MainCertificateFinderTest::testGetLastMainCertificatePairNoFiles": 0.005, "Cdn77\\NxgApi\\Tests\\Functional\\Clap\\OriginTest::testOk": 2.607, "Cdn77\\NxgApi\\Tests\\Functional\\Clap\\OriginUpdaterTest::testOk": 0.273, "Cdn77\\NxgApi\\Tests\\Functional\\Clap\\OriginUpdaterTest::testResourceWithMultipleOrigins": 0.022, "Cdn77\\NxgApi\\Tests\\Functional\\Clap\\OriginUpdaterTest::testResourceWithMultipleOriginsAndOneWithClapId": 0.028, "Cdn77\\NxgApi\\Tests\\Functional\\Clap\\OriginUpdaterTest::testMultipleResources": 0.03, "Cdn77\\NxgApi\\Tests\\Functional\\Clap\\OriginUpdaterTest::testMultipleResourcesWithoutClapOriginId": 0.335, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testFailsOnTriggerWhenAddingExistingCname": 0.144, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testFailsOnTriggerWhenAddingExistingCname with data set \"test 1\"": 0.028, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testFailsOnTriggerWhenAddingExistingCname with data set \"test 2\"": 0.118, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testFailsOnTriggerWhenAddingExistingCname with data set \"test 3\"": 0.025, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testFailsOnTriggerWhenAddingExistingCname with data set \"test 4\"": 0.023, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testFailsOnTriggerWhenAddingExistingCname with data set \"case 2\"": 0.034, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testFailsOnTriggerWhenAddingExistingCname with data set \"case 1\"": 0.026, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testFailsWhenAddingExistingCnameWithDifferentCase": 0.242, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname with data set \"test 1\"": 0.031, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname with data set \"test 2\"": 0.035, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname with data set \"case 1\"": 2.57, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname with data set \"test 3\"": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname with data set \"case 2\"": 0.145, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsWhenAddingExistingCnameWithDifferentCase": 0.061, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname with data set \"duplicate-on-second-new-entry\"": 0.434, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname with data set \"duplicate-within-new-list-itself\"": 0.41, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname with data set \"duplicate-with-surrounding-whitespace\"": 0.386, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname with data set \"duplicate-with-surrounding-whitespace 2\"": 0.036, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testOkWithCnameAsSubstring": 0.03, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testOkWithCnameAsSubstring": 0.029, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname": 2.675, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname with data set \"case insensitive 2\"": 0.031, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname with data set \"duplicate-with-surrounding-whitespace 1\"": 0.056, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testFailsOnTriggerWhenAddingExistingCname with data set \"case insensitive 1\"": 0.03, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testFailsOnTriggerWhenAddingExistingCname with data set \"duplicate-with-surrounding-whitespace 1\"": 0.024, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testFailsOnTriggerWhenAddingExistingCname with data set \"duplicate-with-surrounding-whitespace 2\"": 0.025, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\EditTest::testOkWithEmptyCname": 0.034, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Cname\\AddTest::testOkWithEmptyCname": 0.068, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\CreateControllerTest::testTooOldRequestCancelled": 0.114, "Cdn77\\NxgApi\\Tests\\Functional\\Service\\LetsEncrypt\\RenewalManagerTest::testEnqueueOldCertificatesForRenewalWhenTooOldCancelledIsNotTheLastRequest": 0.05, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\CreateControllerTest::testInactiveResourceRequestCancelled": 4.351, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\CreateControllerTest::testLongTimeSuspendedResourceRequestCancelled": 0.165, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\CreateControllerTest::testDeletedResourceRequestCancelled": 0.143, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\StatusControllerTest::testWithInstantSslWithCancelledRequestWhenActiveCertificate": 0.043, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\ListControllerTest::testWithInactiveResources": 0.427, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\ListControllerTest::testWithInactiveSuspendedResource": 0.142, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\ListControllerTest::testWithInactiveDeletedResource": 1.02, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\ListControllerTest::testWithInactiveDeletedResourceLikeProduction": 0.558, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\ListControllerProductionLikeTest::testWithInactiveDeletedResourceLikeProduction": 0.403, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Controller\\ListControllerProductionLikeTest::testWithInactiveDeletedResourceWithoutFlushShouldFail": 0.214, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Console\\InactiveTest::testWithInactiveDeletedResource": 13.778, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Console\\InactiveTest::testWithInactiveSuspendedResource": 1.07, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Console\\InactiveResourceCancellationCommandTest::testWithInactiveDeletedResource": 104.738, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Console\\InactiveResourceCancellationCommandTest::testLimit": 0.161, "Cdn77\\NxgApi\\Tests\\Functional\\LetsEncrypt\\Application\\Console\\InactiveResourceCancellationCommandTest::testWithInactiveResources": 0.455, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Console\\ResourceOriginComparisonCommandTest::testCompareWithNonExistentFile": 0.04, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Console\\ResourceOriginComparisonCommandTest::testCompareWithInvalidJson": 0.029, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Console\\ResourceOriginComparisonCommandTest::testCompareWithDifferences": 3.037, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Console\\ResourceOriginComparisonCommandTest::testCompareWithMatchingData": 0.056, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Console\\ResourceOriginComparisonCommandTest::testCompareWithWrongOrder": 0.062, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Console\\ResourceOriginComparisonCommandTest::testCompareWithMissingOrigins": 0.063, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\LoadControllerTest::testLoadForMultipleStoredCertificates": 1.195, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\LoadControllerTest::testLoadForSpecificCertificate": 2.404, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\LoadControllerTest::testLoadWhenAccountUuidDirMissing": 0.038, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\LoadControllerTest::testLoadWhenAccountDirMissing": 1.051, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteWithInvalidAccountId": 0.231, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteWithUnknownAccount": 0.016, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteOnePairFromMultiple": 1.566, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteWithInvalidUuid": 0.597, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteWithUnknownUuid": 0.009, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteLastPair": 0.148, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testFilesystemWorks": 0.308, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testHandlerDirectly": 0.486, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithEmptyUuid": 0.624, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithEmptyCertificate": 0.339, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testRewriteCertificateWithSameUuid": 0.772, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testAddCertificateToExistingAccount": 0.584, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testAddCertificateToNewAccount": 0.286, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithEmptyKey": 0.393, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testFailAddInvalidCertificatePair": 0.497, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithInvalidAccountId": 0.471, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithInvalidUuidFormat": 0.304, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithNonV4Uuid": 1.11, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithInvalidUuid with data set \"too short\"": 0.467, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithInvalidUuid with data set \"empty string\"": 0.14, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithInvalidUuid with data set \"uuid v5\"": 0.094, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithInvalidUuid with data set \"malformed uuid\"": 0.696, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithInvalidUuid with data set \"invalid characters\"": 0.511, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithInvalidUuid with data set \"uuid v3\"": 0.163, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithInvalidUuid with data set \"uuid v1\"": 0.502, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testStoreWithInvalidUuid with data set \"invalid format\"": 0.109, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\LoadControllerTest::testLoadWithInvalidUuid with data set \"uuid v1\"": 1.982, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\LoadControllerTest::testLoadWithInvalidUuid with data set \"empty string\"": 0.759, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\LoadControllerTest::testLoadWithInvalidUuid with data set \"invalid format\"": 1.317, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\LoadControllerTest::testLoadWithInvalidUuid with data set \"uuid v3\"": 1.722, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\LoadControllerTest::testLoadWithInvalidUuid with data set \"invalid characters\"": 2.207, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\LoadControllerTest::testLoadWithInvalidUuid with data set \"uuid v5\"": 1.019, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\LoadControllerTest::testLoadWithInvalidUuid with data set \"malformed uuid\"": 0.917, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\LoadControllerTest::testLoadWithInvalidUuid with data set \"too short\"": 0.706, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteWithInvalidUuidFormat with data set \"empty string\"": 0.019, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteWithInvalidUuidFormat with data set \"malformed uuid\"": 0.068, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteWithInvalidUuidFormat with data set \"uuid v3\"": 0.032, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteWithInvalidUuidFormat with data set \"invalid format\"": 0.023, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteWithInvalidUuidFormat with data set \"invalid characters\"": 0.019, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteWithInvalidUuidFormat with data set \"uuid v5\"": 0.029, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteWithInvalidUuidFormat with data set \"too short\"": 0.584, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteWithInvalidUuidFormat with data set \"uuid v1\"": 0.224, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\LoadControllerTest::testLoadWithEmptyKeyFile": 0.518, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\LoadControllerTest::testLoadWhenNoKeysExist": 0.014, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteOnePrivateKeyFromMultiple": 0.424, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\DeleteControllerTest::testDeleteLastAccountPrivateKey": 0.418, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testAddPrivateKeyToExistingAccount": 1.163, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testAddPrivateKeyToNewAccount": 0.122, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\StoreControllerTest::testRewritePrivateKeyWithSameUuid": 0.411, "Cdn77\\NxgApi\\Tests\\Functional\\Certificate\\Application\\Controller\\LoadControllerTest::testLoadForMultipleStoredPrivateKeys": 0.475, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithUuidAsPrivateKey": 0.167, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\InstantSsl\\EditTest::testResourceEditWithUuidAsPrivateKey": 0.206, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithNotExistingUuidAsPrivateKey": 0.235, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testUuidWithInvalidCertificatePair": 0.423, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\SetCertificateControllerTest::testWithResourcesUnderDifferentAccounts": 0.459, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\EditControllerTest::testSetCacheParameters": 0.058, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testSetupWithHttpProtection": 0.072, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testSetupWithSsl": 0.212, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testInvalidHotlinkProtection with data set \"invalid geo protection type\"": 0.036, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testInvalidHotlinkProtection with data set \"invalid hotlink protection\"": 0.022, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testInvalidHotlinkProtection with data set \"invalid SSL data\"": 0.052, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\AddControllerTest::testInvalidHotlinkProtection with data set \"invalid IP protection\"": 0.131, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFail with data set \"Over limit\"": 0.65, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFail with data set \"Over limit header\"": 0.622, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFail with data set \"Header name with newline (LF)\"": 2.488, "Cdn77\\NxgApi\\Tests\\Functional\\Resource\\Application\\Controller\\Origins\\OriginHeaders\\AddTest::testFail with data set \"Header name with Unicode line separator\"": 0.404}}