oneup_flysystem:
    adapters:
        certificate_bucket_adapter:
            local:
                location: '%env(CERTIFICATES_PATH)%'

        certificate_cold_adapter:
            local:
                location: '%env(CERTIFICATES_COLD_PATH)%'

    filesystems:
        certificate_bucket:
            adapter: certificate_bucket_adapter

        certificate_cold:
            adapter: certificate_cold_adapter

services:
    League\Flysystem\FilesystemOperator.certificate_bucket:
        alias: 'oneup_flysystem.certificate_bucket_filesystem'

    League\Flysystem\FilesystemOperator.certificate_cold:
        alias: 'oneup_flysystem.certificate_cold_filesystem'
