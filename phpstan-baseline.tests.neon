parameters:
	ignoreErrors:
		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 2
			path: tests/Functional/Account/Application/Controller/DatacenterListControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 2
			path: tests/Functional/Controller/Internal/Server/DetailControllerTest.php

		-
			message: "#^Cannot call method getForcedState\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server\\|null\\.$#"
			count: 3
			path: tests/Functional/Controller/Internal/Server/Force/ForceDownControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Controller/Internal/Server/Force/ForceDownControllerTest.php

		-
			message: "#^Parameter \\#1 \\$server of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Controller\\\\Internal\\\\Server\\\\Force\\\\ForceDownControllerTest\\:\\:assertCurrentServerStatus\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server, Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server\\|null given\\.$#"
			count: 2
			path: tests/Functional/Controller/Internal/Server/Force/ForceDownControllerTest.php

		-
			message: "#^Parameter \\#1 \\$server of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Controller\\\\Internal\\\\Server\\\\Force\\\\ForceDownControllerTest\\:\\:assertServerLastDownStatus\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server, Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server\\|null given\\.$#"
			count: 2
			path: tests/Functional/Controller/Internal/Server/Force/ForceDownControllerTest.php

		-
			message: "#^Cannot call method getForcedState\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server\\|null\\.$#"
			count: 3
			path: tests/Functional/Controller/Internal/Server/Force/ForceUpControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Controller/Internal/Server/Force/ForceUpControllerTest.php

		-
			message: "#^Parameter \\#1 \\$server of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Controller\\\\Internal\\\\Server\\\\Force\\\\ForceUpControllerTest\\:\\:assertCurrentServerStatus\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server, Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server\\|null given\\.$#"
			count: 2
			path: tests/Functional/Controller/Internal/Server/Force/ForceUpControllerTest.php

		-
			message: "#^Parameter \\#1 \\$server of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Controller\\\\Internal\\\\Server\\\\Force\\\\ForceUpControllerTest\\:\\:assertNoServerLastDownStatus\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server, Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server\\|null given\\.$#"
			count: 1
			path: tests/Functional/Controller/Internal/Server/Force/ForceUpControllerTest.php

		-
			message: "#^Parameter \\#1 \\$server of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Controller\\\\Internal\\\\Server\\\\Force\\\\ForceUpControllerTest\\:\\:assertServerLastDownStatus\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server, Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server\\|null given\\.$#"
			count: 1
			path: tests/Functional/Controller/Internal/Server/Force/ForceUpControllerTest.php

		-
			message: "#^Cannot call method getForcedState\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server\\|null\\.$#"
			count: 3
			path: tests/Functional/Controller/Internal/Server/Force/UnforceControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Controller/Internal/Server/Force/UnforceControllerTest.php

		-
			message: "#^Parameter \\#1 \\$server of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Controller\\\\Internal\\\\Server\\\\Force\\\\UnforceControllerTest\\:\\:assertCurrentServerStatus\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server, Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server\\|null given\\.$#"
			count: 2
			path: tests/Functional/Controller/Internal/Server/Force/UnforceControllerTest.php

		-
			message: "#^Parameter \\#1 \\$server of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Controller\\\\Internal\\\\Server\\\\Force\\\\UnforceControllerTest\\:\\:assertNoServerLastDownStatus\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server, Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server\\|null given\\.$#"
			count: 1
			path: tests/Functional/Controller/Internal/Server/Force/UnforceControllerTest.php

		-
			message: "#^Parameter \\#1 \\$server of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Controller\\\\Internal\\\\Server\\\\Force\\\\UnforceControllerTest\\:\\:assertServerLastDownStatus\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server, Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server\\|null given\\.$#"
			count: 1
			path: tests/Functional/Controller/Internal/Server/Force/UnforceControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 2
			path: tests/Functional/Controller/Internal/Server/ListControllerTest.php

		-
			message: "#^Cannot call method getManager\\(\\) on object\\|null\\.$#"
			count: 1
			path: tests/Functional/Controller/Legacy/Groups/ListControllerTest.php

		-
			message: "#^Dynamic call to static method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\WebTestCase\\:\\:getDefaultHeaders\\(\\)\\.$#"
			count: 2
			path: tests/Functional/Controller/Legacy/Groups/ListControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 2
			path: tests/Functional/Controller/Legacy/Groups/ListControllerTest.php

		-
			message: "#^You should use assertCount\\(\\$expectedCount, \\$variable\\) instead of assertSame\\(\\$expectedCount, count\\(\\$variable\\)\\)\\.$#"
			count: 1
			path: tests/Functional/Controller/Legacy/Groups/ListControllerTest.php

		-
			message: "#^Dynamic call to static method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\WebTestCase\\:\\:getDefaultHeaders\\(\\)\\.$#"
			count: 3
			path: tests/Functional/Controller/Legacy/Resources/DetailControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Controller/Legacy/Resources/DetailControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 2
			path: tests/Functional/Controller/Legacy/Resources/DomainLookupControllerTest.php

		-
			message: "#^Parameter \\#1 \\$resource of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Controller\\\\Legacy\\\\Resources\\\\DomainLookupControllerTest\\:\\:formatExpectedResponse\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource, Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource\\|null given\\.$#"
			count: 2
			path: tests/Functional/Controller/Legacy/Resources/DomainLookupControllerTest.php

		-
			message: "#^Call to function assert\\(\\) with true will always evaluate to true\\.$#"
			count: 1
			path: tests/Functional/Controller/Legacy/Resources/ListControllerTest.php

		-
			message: "#^Dynamic call to static method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\WebTestCase\\:\\:getDefaultHeaders\\(\\)\\.$#"
			count: 3
			path: tests/Functional/Controller/Legacy/Resources/ListControllerTest.php

		-
			message: "#^Instanceof between Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource and Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource will always evaluate to true\\.$#"
			count: 1
			path: tests/Functional/Controller/Legacy/Resources/ListControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 3
			path: tests/Functional/Controller/Legacy/Resources/ListControllerTest.php

		-
			message: "#^You should use assertCount\\(\\$expectedCount, \\$variable\\) instead of assertSame\\(\\$expectedCount, count\\(\\$variable\\)\\)\\.$#"
			count: 2
			path: tests/Functional/Controller/Legacy/Resources/ListControllerTest.php

		-
			message: "#^Call to static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertNotNull\\(\\) with Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Ssl will always evaluate to true\\.$#"
			count: 4
			path: tests/Functional/Controller/Legacy/Resources/Ssl/SetControllerTest.php

		-
			message: "#^Cannot call method getCreatedAt\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\SslFile\\|null\\.$#"
			count: 1
			path: tests/Functional/Controller/Legacy/Resources/Ssl/SetControllerTest.php

		-
			message: "#^Cannot call method getId\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\SslFile\\|null\\.$#"
			count: 1
			path: tests/Functional/Controller/Legacy/Resources/Ssl/SetControllerTest.php

		-
			message: "#^Cannot call method getIndex\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\SslFile\\|null\\.$#"
			count: 1
			path: tests/Functional/Controller/Legacy/Resources/Ssl/SetControllerTest.php

		-
			message: "#^Cannot call method hasInstantSsl\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource\\|null\\.$#"
			count: 1
			path: tests/Functional/Controller/Legacy/Resources/Ssl/SetControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 4
			path: tests/Functional/Controller/Legacy/Resources/Ssl/SetControllerTest.php

		-
			message: "#^You should use assertCount\\(\\$expectedCount, \\$variable\\) instead of assertSame\\(\\$expectedCount, \\$variable\\-\\>count\\(\\)\\)\\.$#"
			count: 4
			path: tests/Functional/Controller/Legacy/Resources/Ssl/SetControllerTest.php

		-
			message: "#^Parameter \\#2 \\$actualJson of static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertJsonStringEqualsJsonString\\(\\) expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Core/Application/Controller/VersionControllerTest.php

		-
			message: "#^Call to static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertIsInt\\(\\) with int will always evaluate to true\\.$#"
			count: 1
			path: tests/Functional/Entity/Legacy/Id/ResourceIdGeneratorTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 2
			path: tests/Functional/FullLogs/Application/Controller/StatusControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 2
			path: tests/Functional/Ip/Application/Controller/StatusControllerTest.php

		-
			message: "#^Cannot call method getManager\\(\\) on object\\|null\\.$#"
			count: 1
			path: tests/Functional/KernelTestCase.php

		-
			message: "#^Call to static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertNotNull\\(\\) with Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Ssl will always evaluate to true\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^Cannot call method getDescription\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\Result\\|null\\.$#"
			count: 3
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^Cannot call method getRequest\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\Result\\|null\\.$#"
			count: 4
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^Cannot call method getStatus\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\Result\\|null\\.$#"
			count: 2
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 2
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^You should use assertCount\\(\\$expectedCount, \\$variable\\) instead of assertSame\\(\\$expectedCount, \\$variable\\-\\>count\\(\\)\\)\\.$#"
			count: 1
			path: tests/Functional/LetsEncrypt/Application/Controller/CreateControllerTest.php

		-
			message: "#^Call to function array_search\\(\\) requires parameter \\#3 to be set\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/AddControllerTest.php

		-
			message: "#^Call to function in_array\\(\\) requires parameter \\#3 to be set\\.$#"
			count: 4
			path: tests/Functional/Resource/Application/Controller/AddControllerTest.php

		-
			message: "#^Cannot call method getHttpsRedirectCode\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource\\|null\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/AddControllerTest.php

		-
			message: "#^PHPDoc tag @return contains unresolvable type\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/AddControllerTest.php

		-
			message: "#^You should use assertCount\\(\\$expectedCount, \\$variable\\) instead of assertSame\\(\\$expectedCount, count\\(\\$variable\\)\\)\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/AddControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/DatacenterListControllerTest.php

		-
			message: "#^Call to static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertInstanceOf\\(\\) with 'Cdn77\\\\\\\\NxgApi\\\\\\\\Entity\\\\\\\\Legacy\\\\\\\\CustomLocation' and Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CustomLocation will always evaluate to true\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/EditControllerTest.php

		-
			message: "#^Call to static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertNotNull\\(\\) with Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\ResourceGeoProtection will always evaluate to true\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/EditControllerTest.php

		-
			message: "#^Call to static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertNotNull\\(\\) with Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\ResourceIpProtection will always evaluate to true\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/EditControllerTest.php

		-
			message: "#^Call to static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertNotNull\\(\\) with Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\ResourceRefererProtection will always evaluate to true\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/EditControllerTest.php

		-
			message: "#^Cannot call method getTimestamp\\(\\) on DateTimeImmutable\\|null\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/EditControllerTest.php

		-
			message: "#^PHPDoc tag @return contains unresolvable type\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/EditControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 16
			path: tests/Functional/Resource/Application/Controller/EditControllerTest.php

		-
			message: "#^You should use assertCount\\(\\$expectedCount, \\$variable\\) instead of assertSame\\(\\$expectedCount, \\$variable\\-\\>count\\(\\)\\)\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/EditControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/EnableDatacentersControllerTest.php

		-
			message: "#^Generator expects value type array\\<array\\<int\\>\\|null\\>, array\\<int, array\\<int, int\\>\\|true\\> given\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/AddTest.php

		-
			message: "#^Generator expects value type array\\<array\\<int\\>\\|null\\>, array\\<int, array\\|false\\> given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/AddTest.php

		-
			message: "#^Generator expects value type array\\<array\\<int\\>\\|null\\>, array\\<int, array\\|true\\> given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/AddTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\FollowRedirect\\\\AddTest\\:\\:callApiGetResponse\\(\\) has parameter \\$dataForNewCdn with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/AddTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\FollowRedirect\\\\AddTest\\:\\:testOk\\(\\) has parameter \\$followRedirectCodes with no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/AddTest.php

		-
			message: "#^PHPDoc tag @param for parameter \\$dataForNewCdn with type array\\<int\\>\\|null is not subtype of native type array\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/AddTest.php

		-
			message: "#^PHPDoc tag @param for parameter \\$followRedirectCodes with type array\\<int, int\\>\\|null is not subtype of native type array\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/AddTest.php

		-
			message: "#^PHPDoc tag @return has invalid value \\(Generator\\<string, array\\<array\\<int\\>\\>\\)\\: Unexpected token \"\\*/\", expected '\\>' at offset 48$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/AddTest.php

		-
			message: "#^Generator expects value type array\\<array\\<int\\>\\|null\\>, array\\<int, array\\<int, int\\>\\|true\\> given\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/EditTest.php

		-
			message: "#^Generator expects value type array\\<array\\<int\\>\\|null\\>, array\\<int, array\\|false\\> given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/EditTest.php

		-
			message: "#^Generator expects value type array\\<array\\<int\\>\\|null\\>, array\\<int, array\\|true\\> given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/EditTest.php

		-
			message: "#^Implicit array creation is not allowed \\- variable \\$data does not exist\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/EditTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\FollowRedirect\\\\EditTest\\:\\:providerBadFollowRedirect\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/EditTest.php

		-
			message: "#^PHPDoc tag @return contains unresolvable type\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/FollowRedirect/EditTest.php

		-
			message: "#^Cannot call method save\\(\\) on object\\|null\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/GetCertificateControllerTest.php

		-
			message: "#^Call to static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertNotNull\\(\\) with Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Ssl will always evaluate to true\\.$#"
			count: 5
			path: tests/Functional/Resource/Application/Controller/InstantSsl/EditTest.php

		-
			message: "#^Cannot call method getIndex\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\SslFile\\|null\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/InstantSsl/EditTest.php

		-
			message: "#^Cannot call method getType\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\SslFile\\|false\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/InstantSsl/EditTest.php

		-
			message: "#^Cannot call method hasInstantSsl\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource\\|null\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/InstantSsl/EditTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/InstantSsl/EditTest.php

		-
			message: "#^You should use assertCount\\(\\$expectedCount, \\$variable\\) instead of assertSame\\(\\$expectedCount, \\$variable\\-\\>count\\(\\)\\)\\.$#"
			count: 5
			path: tests/Functional/Resource/Application/Controller/InstantSsl/EditTest.php

		-
			message: "#^You should use assertCount\\(\\$expectedCount, \\$variable\\) instead of assertSame\\(\\$expectedCount, count\\(\\$variable\\)\\)\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/InstantSsl/EditTest.php

		-
			message: "#^Cannot call method getSecureLinkExpiryParam\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\SecureToken\\|null\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/AddTest.php

		-
			message: "#^Cannot call method getSecureLinkPathlenParam\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\SecureToken\\|null\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/AddTest.php

		-
			message: "#^Cannot call method getSecureLinkSecretParam\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\SecureToken\\|null\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/AddTest.php

		-
			message: "#^Cannot call method getSecureLinkTokenParam\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\SecureToken\\|null\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/AddTest.php

		-
			message: "#^Cannot call method getType\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\SecureToken\\|null\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/AddTest.php

		-
			message: "#^Cannot call method getValue\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\SecureToken\\|null\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/AddTest.php

		-
			message: "#^Cannot call method hasSecureLinkRewritePlaylist\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\SecureToken\\|null\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/AddTest.php

		-
			message: "#^PHPDoc tag @return contains unresolvable type\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/SecureToken/AddTest.php

		-
			message: "#^Parameter \\#1 \\$value of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\SecureToken\\\\AddTest\\:\\:nullingEmptyString\\(\\) expects string, string\\|null given\\.$#"
			count: 4
			path: tests/Functional/Resource/Application/Controller/SecureToken/AddTest.php

		-
			message: "#^Cannot call method getId\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource\\|null\\.$#"
			count: 4
			path: tests/Functional/Resource/Application/Controller/SecureToken/EditTest.php

		-
			message: "#^Cannot call method getResourceSecureToken\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource\\|null\\.$#"
			count: 27
			path: tests/Functional/Resource/Application/Controller/SecureToken/EditTest.php

		-
			message: "#^PHPDoc tag @return contains unresolvable type\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/EditTest.php

		-
			message: "#^Parameter \\#1 \\$value of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\SecureToken\\\\EditTest\\:\\:nullingEmptyString\\(\\) expects string, bool\\|string given\\.$#"
			count: 4
			path: tests/Functional/Resource/Application/Controller/SecureToken/EditTest.php

		-
			message: "#^Parameter \\#2 \\$data of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\SecureToken\\\\EditTest\\:\\:callApiGetResponse\\(\\) expects array\\<string, string\\|null\\>\\|null, array\\<string, bool\\|string\\> given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/EditTest.php

		-
			message: "#^Parameter \\#2 \\$data of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\SecureToken\\\\EditTest\\:\\:callApiGetResponse\\(\\) expects array\\<string, string\\|null\\>\\|null, array\\<string, bool\\|string\\|null\\> given\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/SecureToken/EditTest.php

		-
			message: "#^Parameter \\#2 \\$secureTokenType of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\SecureToken\\\\EditTest\\:\\:enableResourceSecureToken\\(\\) expects string, bool\\|string given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/EditTest.php

		-
			message: "#^Parameter \\#3 \\$secureTokenValue of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\SecureToken\\\\EditTest\\:\\:enableResourceSecureToken\\(\\) expects string, bool\\|string given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/EditTest.php

		-
			message: "#^Parameter \\#4 \\$secureLinkExpiryParam of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\SecureToken\\\\EditTest\\:\\:enableResourceSecureToken\\(\\) expects string\\|null, bool\\|string given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/EditTest.php

		-
			message: "#^Parameter \\#5 \\$secureLinkTokenParam of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\SecureToken\\\\EditTest\\:\\:enableResourceSecureToken\\(\\) expects string\\|null, bool\\|string given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/EditTest.php

		-
			message: "#^Parameter \\#6 \\$secureLinkPathlenParam of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\SecureToken\\\\EditTest\\:\\:enableResourceSecureToken\\(\\) expects string\\|null, bool\\|string given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/EditTest.php

		-
			message: "#^Parameter \\#7 \\$secureLinkSecretParam of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\SecureToken\\\\EditTest\\:\\:enableResourceSecureToken\\(\\) expects string\\|null, bool\\|string given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/EditTest.php

		-
			message: "#^Parameter \\#8 \\$secureLinkRewritePlaylist of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\SecureToken\\\\EditTest\\:\\:enableResourceSecureToken\\(\\) expects bool, bool\\|string given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SecureToken/EditTest.php

		-
			message: "#^Call to function array_key_exists\\(\\) with 3 and array\\{false, false, false\\}\\|array\\{false, true, true\\}\\|array\\{true, true, false\\} will always evaluate to false\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/ServersStatusControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 4
			path: tests/Functional/Resource/Application/Controller/ServersStatusControllerTest.php

		-
			message: "#^Call to static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertNotNull\\(\\) with Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Ssl will always evaluate to true\\.$#"
			count: 4
			path: tests/Functional/Resource/Application/Controller/SetCertificateControllerTest.php

		-
			message: "#^Cannot call method hasInstantSsl\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource\\|null\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SetCertificateControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\SetCertificateControllerTest\\:\\:certificatesProvider\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SetCertificateControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SetCertificateControllerTest.php

		-
			message: "#^You should use assertCount\\(\\$expectedCount, \\$variable\\) instead of assertSame\\(\\$expectedCount, \\$variable\\-\\>count\\(\\)\\)\\.$#"
			count: 4
			path: tests/Functional/Resource/Application/Controller/SetCertificateControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 3
			path: tests/Functional/Resource/Application/Controller/SslListControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\SslVerifyDisable\\\\EditTest\\:\\:providerFail\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/SslVerifyDisable/EditTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Application\\\\Controller\\\\StatusControllerTest\\:\\:createDate\\(\\) should return DateTimeImmutable but returns DateTimeImmutable\\|false\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/StatusControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Controller/StatusControllerTest.php

		-
			message: "#^Cannot call method getId\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource\\|null\\.$#"
			count: 2
			path: tests/Functional/Resource/Application/Controller/SuspensionControllerTest.php

		-
			message: "#^Cannot call method isSuspended\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource\\|null\\.$#"
			count: 4
			path: tests/Functional/Resource/Application/Controller/SuspensionControllerTest.php

		-
			message: "#^Cannot call method deserialize\\(\\) on object\\|null\\.$#"
			count: 1
			path: tests/Functional/Resource/Application/Payload/ResourceAddSchemaTest.php

		-
			message: "#^Cannot call method isSuspended\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource\\|null\\.$#"
			count: 2
			path: tests/Functional/Resource/Domain/ResourceSuspenderTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Resource\\\\Domain\\\\ResourceSuspenderTest\\:\\:getSuspender\\(\\) should return Cdn77\\\\NxgApi\\\\Resource\\\\Domain\\\\ResourceSuspender but returns object\\|null\\.$#"
			count: 1
			path: tests/Functional/Resource/Domain/ResourceSuspenderTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 2
			path: tests/Functional/Server/Application/Controller/AllStatusControllerTest.php

		-
			message: "#^Cannot call method isPaused\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server\\|null\\.$#"
			count: 3
			path: tests/Functional/Server/Application/Controller/PauseServerControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Server/Application/Controller/PauseServerControllerTest.php

		-
			message: "#^Parameter \\#1 \\$server of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Server\\\\Application\\\\Controller\\\\PauseServerControllerTest\\:\\:assertCurrentServerStatus\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server, Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server\\|null given\\.$#"
			count: 2
			path: tests/Functional/Server/Application/Controller/PauseServerControllerTest.php

		-
			message: "#^Parameter \\#1 \\$server of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Server\\\\Application\\\\Controller\\\\PauseServerControllerTest\\:\\:assertServerLastDownStatus\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server, Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server\\|null given\\.$#"
			count: 2
			path: tests/Functional/Server/Application/Controller/PauseServerControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Server/Application/Controller/ServerIdListControllerTest.php

		-
			message: "#^Cannot call method isPaused\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server\\|null\\.$#"
			count: 3
			path: tests/Functional/Server/Application/Controller/UnpauseServerControllerTest.php

		-
			message: "#^Parameter \\#1 \\$json of function Safe\\\\json_decode expects string, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/Server/Application/Controller/UnpauseServerControllerTest.php

		-
			message: "#^Parameter \\#1 \\$server of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Server\\\\Application\\\\Controller\\\\UnpauseServerControllerTest\\:\\:assertCurrentServerStatus\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server, Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server\\|null given\\.$#"
			count: 2
			path: tests/Functional/Server/Application/Controller/UnpauseServerControllerTest.php

		-
			message: "#^Parameter \\#1 \\$server of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Server\\\\Application\\\\Controller\\\\UnpauseServerControllerTest\\:\\:assertNoServerLastDownStatus\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server, Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server\\|null given\\.$#"
			count: 1
			path: tests/Functional/Server/Application/Controller/UnpauseServerControllerTest.php

		-
			message: "#^Parameter \\#1 \\$server of method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Server\\\\Application\\\\Controller\\\\UnpauseServerControllerTest\\:\\:assertServerLastDownStatus\\(\\) expects Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server, Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\Server\\|null given\\.$#"
			count: 1
			path: tests/Functional/Server/Application/Controller/UnpauseServerControllerTest.php

		-
			message: "#^Method Cdn77\\\\NxgApi\\\\Tests\\\\Functional\\\\Server\\\\Domain\\\\ServerPauserTest\\:\\:getPauser\\(\\) should return Cdn77\\\\NxgApi\\\\Server\\\\Domain\\\\ServerPauser but returns object\\|null\\.$#"
			count: 1
			path: tests/Functional/Server/Domain/ServerPauserTest.php

		-
			message: "#^Cannot call method getRunAt\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\Task\\|null\\.$#"
			count: 1
			path: tests/Functional/Service/LetsEncrypt/RenewalManagerTest.php

		-
			message: "#^Cannot call method getState\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\Request\\|null\\.$#"
			count: 2
			path: tests/Functional/Service/LetsEncrypt/RequestManagerTest.php

		-
			message: "#^Method Doctrine\\\\ORM\\\\EntityRepository\\<Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\Task\\>\\:\\:findAll\\(\\) invoked with 1 parameter, 0 required\\.$#"
			count: 1
			path: tests/Functional/Service/LetsEncrypt/RequestManagerTest.php

		-
			message: "#^Only booleans are allowed in a negated boolean, string\\|false given\\.$#"
			count: 1
			path: tests/Functional/WebTestCase.php

		-
			message: "#^Negated boolean expression is always false\\.$#"
			count: 1
			path: tests/Unit/Entity/Legacy/IpTest.php

		-
			message: "#^Access to an undefined static property static\\(Cdn77\\\\NxgApi\\\\Tests\\\\Unit\\\\Entity\\\\LetsEncrypt\\\\RequestTest\\)\\:\\:\\$kernel\\.$#"
			count: 1
			path: tests/Unit/Entity/LetsEncrypt/RequestTest.php

		-
			message: "#^Unsafe call to private method Cdn77\\\\NxgApi\\\\Tests\\\\Unit\\\\Log\\\\Formatter\\\\RequestFormatterTest\\:\\:getTestPrivateKeys\\(\\) through static\\:\\:\\.$#"
			count: 2
			path: tests/Unit/Log/Formatter/RequestFormatterTest.php

		-
			message: "#^Call to static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertIsInt\\(\\) with int will always evaluate to true\\.$#"
			count: 1
			path: tests/Unit/Schema/Internal/Server/ServerDetailSchemaTest.php

		-
			message: "#^Call to static method PHPUnit\\\\Framework\\\\Assert\\:\\:assertIsString\\(\\) with string will always evaluate to true\\.$#"
			count: 1
			path: tests/Unit/Schema/Internal/Server/ServerDetailSchemaTest.php

		-
			message: "#^Access to an undefined static property static\\(Cdn77\\\\NxgApi\\\\Tests\\\\Unit\\\\Schema\\\\Legacy\\\\Resources\\\\ResourceDetailInfoTest\\)\\:\\:\\$kernel\\.$#"
			count: 1
			path: tests/Unit/Schema/Legacy/Resources/ResourceDetailInfoTest.php

		-
			message: "#^Call to function assert\\(\\) with true will always evaluate to true\\.$#"
			count: 2
			path: tests/Unit/Schema/Legacy/Resources/ResourceDetailInfoTest.php

		-
			message: "#^Cannot access property \\$secureLinkExpiryParam on Cdn77\\\\NxgApi\\\\Resource\\\\Application\\\\Payload\\\\SecureTokenDetailSchema\\|null\\.$#"
			count: 1
			path: tests/Unit/Schema/Legacy/Resources/ResourceDetailInfoTest.php

		-
			message: "#^Cannot access property \\$secureLinkPathlenParam on Cdn77\\\\NxgApi\\\\Resource\\\\Application\\\\Payload\\\\SecureTokenDetailSchema\\|null\\.$#"
			count: 1
			path: tests/Unit/Schema/Legacy/Resources/ResourceDetailInfoTest.php

		-
			message: "#^Cannot access property \\$secureLinkRewritePlaylist on Cdn77\\\\NxgApi\\\\Resource\\\\Application\\\\Payload\\\\SecureTokenDetailSchema\\|null\\.$#"
			count: 1
			path: tests/Unit/Schema/Legacy/Resources/ResourceDetailInfoTest.php

		-
			message: "#^Cannot access property \\$secureLinkSecretParam on Cdn77\\\\NxgApi\\\\Resource\\\\Application\\\\Payload\\\\SecureTokenDetailSchema\\|null\\.$#"
			count: 1
			path: tests/Unit/Schema/Legacy/Resources/ResourceDetailInfoTest.php

		-
			message: "#^Cannot access property \\$secureLinkTokenParam on Cdn77\\\\NxgApi\\\\Resource\\\\Application\\\\Payload\\\\SecureTokenDetailSchema\\|null\\.$#"
			count: 1
			path: tests/Unit/Schema/Legacy/Resources/ResourceDetailInfoTest.php

		-
			message: "#^Cannot access property \\$secureTokenType on Cdn77\\\\NxgApi\\\\Resource\\\\Application\\\\Payload\\\\SecureTokenDetailSchema\\|null\\.$#"
			count: 1
			path: tests/Unit/Schema/Legacy/Resources/ResourceDetailInfoTest.php

		-
			message: "#^Cannot access property \\$secureTokenValue on Cdn77\\\\NxgApi\\\\Resource\\\\Application\\\\Payload\\\\SecureTokenDetailSchema\\|null\\.$#"
			count: 1
			path: tests/Unit/Schema/Legacy/Resources/ResourceDetailInfoTest.php

		-
			message: "#^Cannot call method getSecureLinkExpiryParam\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\SecureToken\\|null\\.$#"
			count: 1
			path: tests/Unit/Schema/Legacy/Resources/ResourceDetailInfoTest.php

		-
			message: "#^Cannot call method getSecureLinkPathlenParam\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\SecureToken\\|null\\.$#"
			count: 1
			path: tests/Unit/Schema/Legacy/Resources/ResourceDetailInfoTest.php

		-
			message: "#^Cannot call method getSecureLinkSecretParam\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\SecureToken\\|null\\.$#"
			count: 1
			path: tests/Unit/Schema/Legacy/Resources/ResourceDetailInfoTest.php

		-
			message: "#^Cannot call method getSecureLinkTokenParam\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\SecureToken\\|null\\.$#"
			count: 1
			path: tests/Unit/Schema/Legacy/Resources/ResourceDetailInfoTest.php

		-
			message: "#^Cannot call method getType\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\SecureToken\\|null\\.$#"
			count: 1
			path: tests/Unit/Schema/Legacy/Resources/ResourceDetailInfoTest.php

		-
			message: "#^Cannot call method getValue\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\SecureToken\\|null\\.$#"
			count: 1
			path: tests/Unit/Schema/Legacy/Resources/ResourceDetailInfoTest.php

		-
			message: "#^Cannot call method hasSecureLinkRewritePlaylist\\(\\) on Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\SecureToken\\|null\\.$#"
			count: 1
			path: tests/Unit/Schema/Legacy/Resources/ResourceDetailInfoTest.php

		-
			message: "#^Instanceof between Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource and Cdn77\\\\NxgApi\\\\Entity\\\\Legacy\\\\CdnResource will always evaluate to true\\.$#"
			count: 2
			path: tests/Unit/Schema/Legacy/Resources/ResourceDetailInfoTest.php

		-
			message: "#^Short ternary operator is not allowed\\. Use null coalesce operator if applicable or consider using long ternary\\.$#"
			count: 1
			path: tests/Unit/Service/Legacy/Certificate/OpenSSLCertificateMetadataParserTest.php

		-
			message: "#^Call to an undefined method Doctrine\\\\ORM\\\\EntityRepository\\|Mockery\\\\MockInterface\\:\\:shouldReceive\\(\\)\\.$#"
			count: 1
			path: tests/Unit/Service/Server/ServerStatusManagerTest.php

		-
			message: "#^Property Cdn77\\\\NxgApi\\\\Tests\\\\Unit\\\\Service\\\\Server\\\\ServerStatusManagerTest\\:\\:\\$repositoryTypeMap with generic class Doctrine\\\\ORM\\\\EntityRepository does not specify its types\\: TEntityClass$#"
			count: 1
			path: tests/Unit/Service/Server/ServerStatusManagerTest.php

		-
			message: "#^Generator expects value type array\\<Cdn77\\\\NxgApi\\\\Validator\\\\Constraints\\\\Certificate\\\\CertificatePair\\>, array\\<int, Cdn77\\\\NxgApi\\\\Entity\\\\LetsEncrypt\\\\CertificatePair\\> given\\.$#"
			count: 2
			path: tests/Unit/Validator/Constraints/Certificate/CertificatePairValidatorTest.php

		-
			message: "#^Function define is unsafe to use\\. It can return FALSE instead of throwing an exception\\. Please add 'use function Safe\\\\define;' at the beginning of the file to use the variant provided by the 'thecodingmachine/safe' library\\.$#"
			count: 1
			path: tests/bootstrap.php
