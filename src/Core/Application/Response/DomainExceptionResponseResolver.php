<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Application\Response;

use Cdn77\NxgApi\Core\Domain\Exception\NxgApiDomainException;
use Cdn77\NxgApi\Entity\LetsEncrypt\Exception\RequestFinished;
use Cdn77\NxgApi\LetsEncrypt\Domain\Exception\FailedToCreateRequest;
use Cdn77\NxgApi\LetsEncrypt\Domain\Exception\RequestNotFound;
use Cdn77\NxgApi\LetsEncrypt\Domain\Exception\TasksNotInQueue;
use Cdn77\NxgApi\Resource\Domain\Exception\FailedToGetCertificate;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use JMS\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

final class DomainExceptionResponseResolver
{
    private SerializerInterface $serializer;

    public function __construct(SerializerInterface $serializer)
    {
        $this->serializer = $serializer;
    }

    public function resolve(NxgApiDomainException $exception): JsonResponse
    {
        return JsonResponse::fromJsonString(
            $this->serializer->serialize(new ErrorsSchema([$exception->getMessage()]), 'json'),
            $this->getResponseCode($exception::class),
        );
    }

    /** @param class-string<NxgApiDomainException> $exceptionClassName */
    private function getResponseCode(string $exceptionClassName): int
    {
        switch ($exceptionClassName) {
            case FailedToCreateRequest::class:
            case RequestFinished::class:
            case RequestNotFound::class:
                return Response::HTTP_UNPROCESSABLE_ENTITY;
            case FailedToGetCertificate::class:
            case TasksNotInQueue::class:
                return Response::HTTP_CONFLICT;
            default:
                return Response::HTTP_NOT_FOUND;
        }
    }
}
