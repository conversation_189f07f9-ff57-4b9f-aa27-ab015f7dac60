<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Core\Domain\Certificate;

use Cdn77\NxgApi\Core\Domain\Repository\SslFileRepository;
use Cdn77\NxgApi\Core\Domain\Repository\SslRepository;
use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\Ssl;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Resource\Domain\SslFileFactory;
use Cdn77\NxgApi\Service\Event\ResourceEvent;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\CertificateBucket;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\Exception\FileCorrupted;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\Exception\FileWriteFailed;
use Cdn77\NxgApi\Service\Legacy\Certificate\CertificateMetadataParser;
use Cdn77\NxgApi\Service\Legacy\Certificate\CertificatePairValidator;
use DateTimeImmutable;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Throwable;

class SslCertificateProcessor
{
    private CertificateBucket $certificateBucket;

    private CertificateMetadataParser $certificateMetadataParser;

    private CertificatePairValidator $certificatePairValidator;

    private EventDispatcherInterface $eventDispatcher;

    private SslFileFactory $sslFileFactory;

    private SslFileRepository $sslFileRepository;

    private SslRepository $sslRepository;

    public function __construct(
        CertificateBucket $certificateBucket,
        CertificateMetadataParser $certificateMetadataParser,
        CertificatePairValidator $certificatePairValidator,
        EventDispatcherInterface $eventDispatcher,
        SslFileFactory $sslFileFactory,
        SslFileRepository $sslFileRepository,
        SslRepository $sslRepository,
    ) {
        $this->certificateBucket = $certificateBucket;
        $this->certificateMetadataParser = $certificateMetadataParser;
        $this->certificatePairValidator = $certificatePairValidator;
        $this->eventDispatcher = $eventDispatcher;
        $this->sslFileFactory = $sslFileFactory;
        $this->sslFileRepository = $sslFileRepository;
        $this->sslRepository = $sslRepository;
    }

    public function createCustomCertificateForResource(
        CdnResource $resource,
        CertificatePair $certificatePair,
    ): SslFile {
        $this->certificatePairValidator->validate($certificatePair);

        $ssl = $this->getOrCreateSslForResource($resource);
        $certificateMetadata = $this->certificateMetadataParser->getMetadata($certificatePair);
        $file = $this->sslFileFactory->createForSsl($certificateMetadata, $ssl, SslFile::TYPE_CUSTOM);

        $ssl->getFiles()->add($file); //nestaci add here? cascade
        $this->sslFileRepository->add($file);

        $ssl->setAssignedAt(new DateTimeImmutable());
        $ssl->setAssignedIndex($file->getIndex());

        try {
            $this->certificateBucket->save(
                $file,
                $certificatePair,
            );
        } catch (FileCorrupted | FileWriteFailed $e) {
            $this->certificateBucket->delete($file);

            throw $e;
        }

        try {
            $this->eventDispatcher->dispatch(new ResourceEvent($resource));
        } catch (Throwable) {
            // This can fail and but it shouldn't affect the response for set certificate request
        }

        return $file;
    }

    public function deleteCertificate(CdnResource $resource): void
    {
        $ssl = $this->sslRepository->findForResource($resource->getId());
        if ($ssl === null) {
            return;
        }

        $this->sslRepository->remove($ssl);

        $this->eventDispatcher->dispatch(new ResourceEvent($resource));
    }

    private function getOrCreateSslForResource(CdnResource $resource): Ssl
    {
        $ssl = $this->sslRepository->findForResource($resource->getId());
        if ($ssl !== null) {
            return $ssl;
        }

        $ssl = new Ssl();
        $ssl->setResource($resource);

        $this->sslRepository->add($ssl);

        return $ssl;
    }
}
