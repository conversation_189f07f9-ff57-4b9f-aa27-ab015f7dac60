<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Application\Controller;

use Cdn77\NxgApi\Core\Application\OpenApi\HasOpenApiPaths;
use Cdn77\NxgApi\Core\Application\OpenApi\Model\Tags;
use Cdn77\NxgApi\Core\Application\OpenApi\PathGenerator;
use Cdn77\NxgApi\Core\Application\Request\ControllerSchemaSerializer;
use Cdn77\NxgApi\Core\Domain\Bus\CommandBus;
use Cdn77\NxgApi\Resource\Application\Payload\SetCertificateSchema;
use Cdn77\NxgApi\Resource\Domain\Command\SetCertificateToMultipleResources;
use Cdn77\NxgApi\Schema\Legacy\ErrorsSchema;
use Cdn77\NxgApi\Service\Legacy\Certificate\Exception\InvalidCertificatePair;
use cebe\openapi\spec\Operation;
use cebe\openapi\spec\PathItem;
use cebe\openapi\spec\Responses;
use J<PERSON>\Serializer\SerializerInterface;
use Stringable;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\ConstraintViolationInterface;

use function array_map;
use function iterator_to_array;

final class SetCertificateController implements HasOpenApiPaths
{
    public const ROUTE_NAME = 'resources.set-certificate';
    private const ROUTE_SUMMARY = 'Set certificate to multiple resources';

    private CommandBus $commandBus;

    private ControllerSchemaSerializer $controllerSchemaSerializer;

    private PathGenerator $pathGenerator;

    private SerializerInterface $serializer;

    public function __construct(
        CommandBus $commandBus,
        ControllerSchemaSerializer $controllerSchemaSerializer,
        PathGenerator $pathGenerator,
        SerializerInterface $serializer,
    ) {
        $this->commandBus = $commandBus;
        $this->controllerSchemaSerializer = $controllerSchemaSerializer;
        $this->pathGenerator = $pathGenerator;
        $this->serializer = $serializer;
    }

    /**
     * @Route(
     *     path="/resources/set-certificate",
     *     methods={Request::METHOD_POST},
     *     name=SetCertificateController::ROUTE_NAME
     * )
     */
    public function execute(Request $request): Response
    {
        $schema = $this->controllerSchemaSerializer->deserialize($request, SetCertificateSchema::class);

        if ($schema instanceof ErrorsSchema) {
            return $this->controllerSchemaSerializer->serializeToResponse($schema);
        }

        try {
            $this->commandBus->handle(SetCertificateToMultipleResources::fromSchema($schema));
        } catch (InvalidCertificatePair $e) {
            return JsonResponse::fromJsonString(
                $this->serializer->serialize(
                    new ErrorsSchema(array_map(
                    /** @return string|Stringable */
                        static fn (ConstraintViolationInterface $violation) => $violation->getMessage(),
                        iterator_to_array($e->getViolations()),
                    )),
                    'json',
                ),
                Response::HTTP_UNPROCESSABLE_ENTITY,
            );
        }

        return new Response('', Response::HTTP_NO_CONTENT);
    }

    /** @inheritDoc */
    public function getPathItems(): array
    {
        $post = new Operation([
            'tags' => [Tags::RESOURCE],
            'summary' => self::ROUTE_SUMMARY,
            'responses' => new Responses([
                Response::HTTP_NO_CONTENT => new \cebe\openapi\spec\Response(
                    ['description' => 'Certificate set to resources'],
                ),
            ]),
        ]);

        return [$this->pathGenerator->generate(self::ROUTE_NAME) => new PathItem(['post' => $post])];
    }
}
