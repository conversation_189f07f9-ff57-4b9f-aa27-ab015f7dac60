<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Resource\Domain\Exception;

use Cdn77\NxgApi\Core\Domain\Exception\NxgApiDomainException;
use DomainException;

use function sprintf;

final class ResourceInvalid extends DomainException implements NxgApiDomainException
{
    public const CNAME_NOT_UNIQUE = 'CNAME "%s" have to be unique in all undeleted resources';

    public static function cnameNotUnique(string $cname): self
    {
        return new self(sprintf(self::CNAME_NOT_UNIQUE, $cname));
    }
}
