<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource;

use Symfony\Component\Validator\Constraint;

/** @Annotation */
class OriginHeaders extends Constraint
{
    public string $forbiddenName = 'Origin header name "%string%" is forbidden.';
    public string $invalidName = 'Origin header name "%string%" contains invalid characters.';
    public string $invalidValue = 'Origin header value "%string%" contains invalid characters.';
    public string $overLimit = 'Maximum size of all origin headers is %d bytes.';
    public string $overLimitHeader = 'Maximum size of origin header (name + value) is %d bytes.';
}
