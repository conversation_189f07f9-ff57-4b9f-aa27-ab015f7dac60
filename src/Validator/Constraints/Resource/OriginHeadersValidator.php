<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Validator\Constraints\Resource;

use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginSchema;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

use function in_array;
use function is_array;
use function Safe\preg_match;
use function strtolower;

final class OriginHeadersValidator extends ConstraintValidator
{
    private const FORBIDDEN_HEADER_NAMES = [
        'via',
        'accept',
        'range',
        'x-real-ip',
    ];

    // RFC 7230 compliant patterns that forbid ASCII control characters (0-31 and 127)
    // Header names: token characters (visible ASCII except separators)
    private const PATTERN_ALLOWED_NAME_CHARS = '/^[!#$%&\'*+\-.0-9A-Z^_`a-z|~]+$/';
    // Header values: visible ASCII characters and horizontal tab, but no other control characters
    private const PATTERN_ALLOWED_VALUE_CHARS = '/^[\x09\x20-\x7E]*$/';

    private const MAX_SIZE = 8192;
    private const MAX_HEADER_SIZE = 1024;

    /** @param mixed $value */
    public function validate($value, Constraint $constraint): void
    {
        if (! $constraint instanceof OriginHeaders) {
            throw new UnexpectedTypeException($constraint, OriginHeaders::class);
        }

        if ($value === null) {
            return;
        }

        if (! is_array($value)) {
            throw new UnexpectedTypeException($value, 'array');
        }

        if ($value === []) {
            return;
        }

        if (strlen(json_encode($value)) > self::MAX_SIZE) {
            $this->context->buildViolation(sprintf($constraint->overLimit, self::MAX_SIZE))
                ->setParameter('%string%', OriginSchema::FIELD_ORIGIN_HEADERS)
                ->addViolation();

            return;
        }

        foreach ($value as $headerName => $headerValue) {
            if (strlen($headerName . $headerValue) > self::MAX_HEADER_SIZE) {
                $this->context->buildViolation(sprintf($constraint->overLimitHeader, self::MAX_HEADER_SIZE))
                    ->setParameter('%string%', $headerName)
                    ->addViolation();
            }

            if (in_array(strtolower($headerName), self::FORBIDDEN_HEADER_NAMES, true)) {
                $this->context->buildViolation($constraint->forbiddenName)
                    ->setParameter('%string%', $headerName)
                    ->addViolation();
            }

            // Validate header name
            if (!$this->isValidHeaderName($headerName)) {
                $this->context->buildViolation($constraint->invalidName)
                    ->setParameter('%string%', $headerName)
                    ->addViolation();
            }

            // Validate header value
            if (!$this->isValidHeaderValue($headerValue)) {
                $this->context->buildViolation($constraint->invalidValue)
                    ->setParameter('%string%', $headerValue)
                    ->addViolation();
            }
        }
    }

    /**
     * Validates header name according to RFC 7230 token rules.
     * Forbids ASCII control characters (0-31 and 127) and separators.
     */
    private function isValidHeaderName(string $headerName): bool
    {
        // Check for empty name
        if ($headerName === '') {
            return false;
        }

        // Check for ASCII control characters (0-31 and 127)
        if ($this->containsControlCharacters($headerName)) {
            return false;
        }

        // Check against RFC 7230 token pattern
        return preg_match(self::PATTERN_ALLOWED_NAME_CHARS, $headerName) === 1;
    }

    /**
     * Validates header value according to RFC 7230 field-content rules.
     * Allows visible ASCII characters and horizontal tab, forbids other control characters.
     */
    private function isValidHeaderValue(string $headerValue): bool
    {
        // Check for ASCII control characters except horizontal tab (0x09)
        if ($this->containsControlCharactersExceptTab($headerValue)) {
            return false;
        }

        // Check against RFC 7230 field-content pattern
        return preg_match(self::PATTERN_ALLOWED_VALUE_CHARS, $headerValue) === 1;
    }

    /**
     * Checks if string contains ASCII control characters (0-31 and 127).
     */
    private function containsControlCharacters(string $value): bool
    {
        // Check for control characters 0-31 and 127 (DEL)
        return preg_match('/[\x00-\x1F\x7F]/', $value) === 1;
    }

    /**
     * Checks if string contains ASCII control characters except horizontal tab (0x09).
     */
    private function containsControlCharactersExceptTab(string $value): bool
    {
        // Check for control characters 0-8, 10-31 and 127 (DEL), excluding tab (0x09)
        return preg_match('/[\x00-\x08\x0A-\x1F\x7F]/', $value) === 1;
    }
}
