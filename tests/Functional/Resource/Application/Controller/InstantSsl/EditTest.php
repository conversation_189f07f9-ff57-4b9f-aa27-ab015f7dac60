<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\InstantSsl;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\Ssl;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Entity\LetsEncrypt\Request as LetsEncryptRequest;
use Cdn77\NxgApi\Entity\LetsEncrypt\RequestState;
use Cdn77\NxgApi\Entity\LetsEncrypt\Task;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditInfo;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceEditSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceSslSchema;
use Cdn77\NxgApi\Service\LetsEncrypt\DomainChooser\CraDomainChooser;
use Cdn77\NxgApi\Service\LetsEncrypt\RequestManager;
use Cdn77\NxgApi\Service\Messaging\Message\ResourceChangeMessage;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Generators\CertificateDefinitions;
use Cdn77\NxgApi\Tests\Generators\CertificatePairGenerator;
use Cdn77\NxgApi\Tests\Utils\FlushAndClear;
use DateTimeImmutable;
use ReflectionProperty;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function array_merge;
use function assert;
use function count;
use function Safe\json_decode;
use function Safe\json_encode;
use function sort;
use function sprintf;

final class EditTest extends WebTestCase
{
    use CertificateDefinitions;
    use NotifyResourceChangeHelper;
    use TemporaryData;

    public function testCertificateRequestIsNotCreatedWhenNoCnamesAreBeingEditedForInstantSsl(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setInstantSsl(true);
        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['ignore_set_cookie' => (int) true],
                    ),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertNoCertificateRequestExists($resource);
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testCertificateRequestIsNotCreatedWhenNoCnamesAreGiven(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setInstantSsl(true);
        FlushAndClear::do($this->getEntityManager());

        $response = $this->makeRequestWithEditingCnames($resource, []);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertNoCertificateRequestExists($resource);
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testExistingCertificateIsUntouchedAndSslNotRemovedWhenInstantSslDisabledAndNoCnamesAreGiven(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setInstantSsl(false);
        $ssl = $this->enableResourceSsl($resource);
        FlushAndClear::do($this->getEntityManager());

        $response = $this->makeRequestWithEditingCnames($resource, []);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertNoCertificateRequestExists($resource);
        self::assertSslExists($resource, $ssl);
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testCertificateRequestIsNotCreatedAndSslRemovedWhenNoCustomCnamesAreGiven(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setCnames([]);
        $resource->setInstantSsl(true);
        $this->enableResourceSsl($resource);
        FlushAndClear::do($this->getEntityManager());

        $response = $this->makeRequestWithEditingCnames($resource, ['foo.r.cdn77.net']);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertNoCertificateRequestExists($resource);
        self::assertSslDoesNotExist($resource);
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testSslIsRemovedWhenNoCustomCnamesAreGiven(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setCnames(['foo.bar', 'foo.c.cdn77.org']);
        $resource->setInstantSsl(true);
        $this->enableResourceSsl($resource);
        FlushAndClear::do($this->getEntityManager());

        $response = $this->makeRequestWithEditingCnames($resource, ['foo.c.cdn77.org']);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        self::assertNoCertificateRequestExists($resource);
        self::assertSslDoesNotExist($resource);
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testCertificateRequestIsCreatedAndSslNotRemovedWhenCnamesAreAddedForInstantSsl(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setInstantSsl(true);
        $resource->setCnames(['foo.example.com']);
        $ssl = $this->enableResourceSsl($resource);
        FlushAndClear::do($this->getEntityManager());

        $this->assertNoCertificateRequestExists($resource);

        $response = $this->makeRequestWithEditingCnames($resource, ['foo-edited.example.com']);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        $updatedResource = $this->getEntityManager()->find(CdnResource::class, $resource->getId());
        self::assertInstanceOf(CdnResource::class, $updatedResource);
        self::assertSame(['foo-edited.example.com'], $updatedResource->getCnames());
        $this->assertOneCertificateRequestExists($updatedResource);
        $this->assertSslExists($updatedResource, $ssl);
        $this->assertUpdatingMessageInQueue($resource->getId());
    }

    public function testCertificateRequestIsCreatedWhenCnamesAreRemovedForInstantSsl(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setInstantSsl(true);
        $resource->setCnames(['foo.example.com', 'bar.example.com']);
        FlushAndClear::do($this->getEntityManager());

        $this->assertNoCertificateRequestExists($resource);

        $response = $this->makeRequestWithEditingCnames($resource, ['foo-edited.example.com']);

        $updatedResource = $this->getEntityManager()->find(CdnResource::class, $resource->getId());
        self::assertInstanceOf(CdnResource::class, $updatedResource);
        self::assertSame(['foo-edited.example.com'], $updatedResource->getCnames());
        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        $this->assertOneCertificateRequestExists($updatedResource);
        $this->assertUpdatingMessageInQueue($resource->getId());
    }

    public function testCertificateRequestIsNotCreatedWhenCnamesAreChangedForInstantSslDisabled(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setInstantSsl(false);
        $resource->setCnames(['foo.example.com']);
        FlushAndClear::do($this->getEntityManager());

        $response = $this->makeRequestWithEditingCnames($resource, ['foo.example.com', 'bar.example.com']);

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertNoCertificateRequestExists($resource);
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testCertificateRequestIsCancelledWhenInstantSslIsDisabled(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setCnames(['foo.example.com']);
        $resource->setInstantSsl(true);

        $requestManager = static::$kernel->getContainer()->get(RequestManager::class);
        assert($requestManager instanceof RequestManager);
        $requestManager->createAndScheduleRequest($resource, new DateTimeImmutable());

        FlushAndClear::do($this->getEntityManager());

        self::assertOneCertificateRequestExists($resource);

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [ResourceEditInfo::FIELD_INSTANT_SSL => (int) false],
                    ),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertNoCertificateRequestExists($resource);
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testInstantSslEnabled(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setInstantSsl(false);
        $this->createResourceIgnoredParameter($resource, 'foo');
        $this->createResourceIgnoredParameter($resource, 'bar');
        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [ResourceEditInfo::FIELD_INSTANT_SSL => (int) true],
                    ),
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $updatedResource = $this->getEntityManager()->find(CdnResource::class, $resource->getId());
        assert($updatedResource instanceof CdnResource);
        self::assertTrue($updatedResource->hasInstantSsl());
        self::assertOneCertificateRequestExists($resource);
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testCertificateRequestIsCancelledWhenCnamesAreRemoved(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setCnames(['foo.example.com']);
        $resource->setInstantSsl(true);

        $requestManager = static::$kernel->getContainer()->get(RequestManager::class);
        assert($requestManager instanceof RequestManager);
        $requestManager->createAndScheduleRequest($resource, new DateTimeImmutable());

        FlushAndClear::do($this->getEntityManager());

        self::assertOneCertificateRequestExists($resource);

        $this->makeRequestWithEditingCnames($resource, []);
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertNoCertificateRequestExists($resource);
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testCertificateRequestIsCreatedForCraWithOneDomain(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setCnames([]);
        $resource->setInstantSsl(true);
        $resource->setAccount($this->createAccount(CraDomainChooser::ACCOUNT_IDS[0]));
        FlushAndClear::do($this->getEntityManager());

        $this->makeRequestWithEditingCnames($resource, [$resource->getId() . '.ssl.cdn.cra.cz']);
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertOneCertificateRequestExistsWithDomains($resource, [$resource->getId() . '.ssl.cdn.cra.cz']);
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testCertificateRequestIsCreatedForCraWithMultipleDomains(): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setInstantSsl(true);
        $resource->setAccount($this->createAccount(CraDomainChooser::ACCOUNT_IDS[0]));
        FlushAndClear::do($this->getEntityManager());

        $this->makeRequestWithEditingCnames(
            $resource,
            [$resource->getId() . '.ssl.cdn.cra.cz', 'test.ssl.cdn.cra.cz'],
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertOneCertificateRequestExistsWithDomains(
            $resource,
            [$resource->getId() . '.ssl.cdn.cra.cz', 'test.ssl.cdn.cra.cz'],
        );
        self::assertUpdatingMessageInQueue($resource->getId());
    }

    public function testEditResourceSslWithNoPreviousCertificateSet(): void
    {
        $now = new DateTimeImmutable();
        $resource = $this->createTemporaryResource();

        $certificatePairGenerator = new CertificatePairGenerator();
        $certificatePair = $certificatePairGenerator->generateRandomCertificatePair();

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [ResourceEditInfo::FIELD_INSTANT_SSL => 0],
                    ),
                    ResourceEditSchema::FIELD_SSL => [
                        ResourceSslSchema::FIELD_CERTIFICATE => $certificatePair->getCertificate(),
                        ResourceSslSchema::FIELD_KEY => $certificatePair->getPrivateKey(),
                    ],
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $ssl = $this->getEntityManager()->getRepository(Ssl::class)->find($resource->getId());

        assert($ssl instanceof Ssl);
        self::assertNotNull($ssl);

        self::assertSame(1, $ssl->getFiles()->count());

        $file = $ssl->getFiles()->first();
        assert($file instanceof SslFile);
        self::assertSame(SslFile::TYPE_CUSTOM, $file->getType());
        self::assertSame($ssl->getAssignedIndex(), $file->getIndex());
        self::assertSame($ssl->getAssignedIndex(), 1);
        self::assertGreaterThanOrEqual($now->getTimestamp(), $ssl->getAssignedAt()->getTimestamp());
        self::assertSame(
            $file->getExpiresAt()->getTimestamp(),
            $this->extractCertificateExpiration($certificatePair)->getTimestamp(),
        );
        self::assertSame([], $this->extractCertificateDomains($certificatePair));
        self::assertMultipleMessagesInQueue([
            [$resource->getId(), ResourceChangeMessage::TYPE_UPDATED],
            [$resource->getId(), ResourceChangeMessage::TYPE_UPDATED],
        ]);
        self::assertNoCertificateRequestExists($resource);
    }

    public function testEditResourceWithExistingPreviousCertificateFiles(): void
    {
        $now = new DateTimeImmutable();
        $resource = $this->createTemporaryResource();
        $resource->setInstantSsl(true);

        $certificatePairGenerator = new CertificatePairGenerator();
        $certificatePair = $certificatePairGenerator->generateRandomCertificatePair();

        $ssl = $this->enableResourceSsl($resource);

        for ($i = 1; $i <= 3; $i++) {
            $sslFile = $this->addResourceSslFile(
                $resource,
                $ssl,
                $i,
                new DateTimeImmutable(),
                ['foo.bar'],
                SslFile::TYPE_LETSENCRYPT,
            );

            $rp = new ReflectionProperty(SslFile::class, 'createdAt');
            $rp->setAccessible(true);
            $rp->setValue($sslFile, new DateTimeImmutable('- ' . ((3 - $i) * 10) . ' minutes'));
        }

        $ssl->setAssignedIndex(2);
        $ssl->setAssignedAt(new DateTimeImmutable('- 15 minutes'));

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [ResourceEditInfo::FIELD_INSTANT_SSL => 0],
                    ),
                    ResourceEditSchema::FIELD_SSL => [
                        ResourceSslSchema::FIELD_CERTIFICATE => $certificatePair->getCertificate(),
                        ResourceSslSchema::FIELD_KEY => $certificatePair->getPrivateKey(),
                    ],
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $ssl = $this->getEntityManager()->getRepository(Ssl::class)->find($resource->getId());
        assert($ssl instanceof Ssl);
        self::assertNotNull($ssl);

        self::assertSame(4, $ssl->getFiles()->count());

        $file = $ssl->getFiles()->get(4);
        assert($file instanceof SslFile);
        self::assertSame(SslFile::TYPE_CUSTOM, $file->getType());
        self::assertSame($ssl->getAssignedIndex(), $file->getIndex());
        self::assertSame($ssl->getAssignedIndex(), 4);
        self::assertGreaterThanOrEqual($now->getTimestamp(), $ssl->getAssignedAt()->getTimestamp());
        self::assertMultipleMessagesInQueue([
            [$resource->getId(), ResourceChangeMessage::TYPE_UPDATED],
            [$resource->getId(), ResourceChangeMessage::TYPE_UPDATED],
        ]);

        $updatedResource = $this->getEntityManager()->getRepository(CdnResource::class)->find($resource->getId());
        self::assertFalse($updatedResource->hasInstantSsl());
        self::assertNoCertificateRequestExists($resource);
    }

    public function testResourceEditWithExistingSslFilesAndMissingSsl(): void
    {
        $now = new DateTimeImmutable();
        $resource = $this->createTemporaryResource();
        $ssl = $this->enableResourceSsl($resource);

        for ($i = 1; $i <= 2; $i++) {
            $this->addResourceSslFile(
                $resource,
                $ssl,
                $i,
                new DateTimeImmutable(),
                ['foo.bar'],
            );
        }

        $this->getEntityManager()->flush();

        $this->getEntityManager()->createQueryBuilder()
            ->delete(Ssl::class, 's')
            ->where('s.resource = :resource')
            ->setParameter('resource', $ssl->getResource())
            ->getQuery()
            ->execute();

        FlushAndClear::do($this->getEntityManager());

        $certificatePairGenerator = new CertificatePairGenerator();
        $certificatePair = $certificatePairGenerator->generateRandomCertificatePair();

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [ResourceEditInfo::FIELD_INSTANT_SSL => 0],
                    ),
                    ResourceEditSchema::FIELD_SSL => [
                        ResourceSslSchema::FIELD_CERTIFICATE => $certificatePair->getCertificate(),
                        ResourceSslSchema::FIELD_KEY => $certificatePair->getPrivateKey(),
                    ],
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $this->getEntityManager()->clear();

        $ssl = $this->getEntityManager()->getRepository(Ssl::class)->find($resource->getId());
        assert($ssl instanceof Ssl);
        self::assertNotNull($ssl);

        $sslFile = $ssl->getFiles()[3];

        self::assertSame(3, $ssl->getFiles()->count());
        self::assertSame($ssl->getAssignedIndex(), $sslFile->getIndex());
        self::assertSame($ssl->getAssignedIndex(), 3);
        self::assertGreaterThanOrEqual($now->getTimestamp(), $ssl->getAssignedAt()->getTimestamp());

        self::assertMultipleMessagesInQueue([
            [$resource->getId(), ResourceChangeMessage::TYPE_UPDATED],
            [$resource->getId(), ResourceChangeMessage::TYPE_UPDATED],
        ]);

        self::assertNoCertificateRequestExists($resource);
    }

    public function testResourceEditWithInvalidCertificatePair(): void
    {
        $resource = $this->createTemporaryResource();

        $certificatePairGenerator = new CertificatePairGenerator();
        $certificatePair = $certificatePairGenerator->generateRandomInvalidCertificatePair();

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [ResourceEditInfo::FIELD_INSTANT_SSL => 0],
                    ),
                    ResourceEditSchema::FIELD_SSL => [
                        ResourceSslSchema::FIELD_CERTIFICATE => $certificatePair->getCertificate(),
                        ResourceSslSchema::FIELD_KEY => $certificatePair->getPrivateKey(),
                    ],
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertNoMessageInQueue();
        self::assertNoCertificateRequestExists($resource);
    }

    public function testResourceEditWithInvalidCertificateChain(): void
    {
        $resource = $this->createTemporaryResource();

        $certificatePairGenerator = new CertificatePairGenerator();
        $certificatePair = $certificatePairGenerator->generateRandomCertificatePair();

        $certificatePairChain = new CertificatePair(
            $certificatePair->getCertificate()
            . $certificatePairGenerator->generateRandomCertificate()
            . "-----BEGIN CERTIFICATE-----\ninvalidCertificate\n-----END CERTIFICATE-----\n"
            . $certificatePairGenerator->generateRandomCertificate(),
            $certificatePair->getPrivateKey(),
        );

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [ResourceEditInfo::FIELD_INSTANT_SSL => 0],
                    ),
                    ResourceEditSchema::FIELD_SSL => [
                        ResourceSslSchema::FIELD_CERTIFICATE => $certificatePairChain->getCertificate(),
                        ResourceSslSchema::FIELD_KEY => $certificatePairChain->getPrivateKey(),
                    ],
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['The certificate chain is malformed, probably 2. intermediate is corrupted.'],
            ],
            json_decode($response->getContent(), true),
        );
        self::assertNoMessageInQueue();
        self::assertNoCertificateRequestExists($resource);
    }

    /**
     * @param list<string> $domains
     *
     * @dataProvider certificatesProvider
     */
    public function testResourceEditWithMultipleCertificateConfigurations(
        CertificatePair $certificatePair,
        array $domains,
        DateTimeImmutable $expiration,
    ): void {
        $now = new DateTimeImmutable();
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [ResourceEditInfo::FIELD_INSTANT_SSL => 0],
                    ),
                    ResourceEditSchema::FIELD_SSL => [
                        ResourceSslSchema::FIELD_CERTIFICATE => $certificatePair->getCertificate(),
                        ResourceSslSchema::FIELD_KEY => $certificatePair->getPrivateKey(),
                    ],
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $ssl = $this->getEntityManager()->getRepository(Ssl::class)->find($resource->getId());
        assert($ssl instanceof Ssl);
        self::assertNotNull($ssl);

        self::assertSame(1, $ssl->getFiles()->count());

        $file = $ssl->getFiles()->first();
        assert($file instanceof SslFile);
        self::assertSame(SslFile::TYPE_CUSTOM, $file->getType());
        self::assertSame($ssl->getAssignedIndex(), $file->getIndex());
        self::assertSame($ssl->getAssignedIndex(), 1);
        self::assertGreaterThanOrEqual($now->getTimestamp(), $ssl->getAssignedAt()->getTimestamp());

        self::assertSame(
            $expiration->getTimestamp(),
            $this->extractCertificateExpiration($certificatePair)->getTimestamp(),
        );
        self::assertSame($file->getExpiresAt()->getTimestamp(), $expiration->getTimestamp());

        self::assertSame($domains, $this->extractCertificateDomains($certificatePair));
        self::assertSame($domains, $file->getDomains());
        self::assertMultipleMessagesInQueue([
            [$resource->getId(), ResourceChangeMessage::TYPE_UPDATED],
            [$resource->getId(), ResourceChangeMessage::TYPE_UPDATED],
        ]);
        self::assertNoCertificateRequestExists($resource);
    }

    /** @return mixed[][] */
    public function certificatesProvider(): iterable
    {
        yield $this->getCertificateWithCommonNameAlternativeNames();
        yield $this->getCertificateWithCommonNameAndOneDifferentAlternativeName();
        yield $this->getCertificateWithCommonNameAndMultipleDifferentAternativeNames();
        yield $this->getCertificateWithCommonNameAndMatchingAlternativeNames();
        yield $this->getCertificateWithCommonNameAndMultipleAndNonDnsAlternativeNames();
    }

    /** @return mixed[][] */
    public function emptySslFieldProvider(): iterable
    {
        yield [null];
        yield [[]];
        yield [[ResourceSslSchema::FIELD_CERTIFICATE => null, ResourceSslSchema::FIELD_KEY => null]];
    }

    public function testResourceEditRemoveSslWithSslNotSet(): void
    {
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [ResourceEditInfo::FIELD_INSTANT_SSL => 0],
                    ),
                    ResourceEditSchema::FIELD_SSL => [],
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertNull($this->getEntityManager()->find(Ssl::class, $resource->getId()));
        self::assertNoCertificateRequestExists($resource);
    }

    public function testResourceEditRemoveExistingSsl(): void
    {
        $resource = $this->createTemporaryResource();
        $this->enableResourceSsl($resource);
        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [ResourceEditInfo::FIELD_INSTANT_SSL => 0],
                    ),
                    ResourceEditSchema::FIELD_SSL => [],
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertNull($this->getEntityManager()->find(Ssl::class, $resource->getId()));
        self::assertMultipleMessagesInQueue([
            [$resource->getId(), ResourceChangeMessage::TYPE_UPDATED],
            [$resource->getId(), ResourceChangeMessage::TYPE_UPDATED],
        ]);
        self::assertNoCertificateRequestExists($resource);
    }

    public function testResourceEditWhereSslFilesAreLeftIntactWhenSslIsDeleted(): void
    {
        $resource = $this->createTemporaryResource();
        $ssl = $this->enableResourceSsl($resource);

        $this->addResourceSslFile($resource, $ssl, 1, new DateTimeImmutable(), ['foo.bar']);
        $this->addResourceSslFile($resource, $ssl, 2, new DateTimeImmutable(), ['bar.baz']);

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [ResourceEditInfo::FIELD_INSTANT_SSL => 0],
                    ),
                    ResourceEditSchema::FIELD_SSL => [],
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        self::assertSame(2, $this->getSslFilesCount($resource));
        self::assertMultipleMessagesInQueue([
            [$resource->getId(), ResourceChangeMessage::TYPE_UPDATED],
            [$resource->getId(), ResourceChangeMessage::TYPE_UPDATED],
        ]);
        self::assertNoCertificateRequestExists($resource);
    }

    /**
     * @param array<string, string>|null $sslField
     *
     * @dataProvider emptySslFieldProvider
     */
    public function testResourceEditChangeCustomCertificateToInstantSslWithEmptySslField(array|null $sslField): void
    {
        $resource = $this->createTemporaryResource();
        $newSsl = $this->enableResourceSsl($resource);

        $this->addResourceSslFile($resource, $newSsl, 1, new DateTimeImmutable(), ['foo.bar']);

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [ResourceEditInfo::FIELD_INSTANT_SSL => 1],
                    ),
                    ResourceEditSchema::FIELD_SSL => $sslField,
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $ssl = $this->getEntityManager()->getRepository(Ssl::class)->find($resource->getId());

        assert($ssl instanceof Ssl);
        self::assertNotNull($ssl);

        $file = $ssl->getFiles()->first();
        self::assertSame(1, $ssl->getFiles()->count());
        self::assertSame(SslFile::TYPE_CUSTOM, $file->getType());

        self::assertOneCertificateRequestExists($resource);

        self::assertMessageInQueue($resource->getId(), ResourceChangeMessage::TYPE_UPDATED);
    }

    /**
     * @param array<string, string>|null $sslField
     *
     * @dataProvider emptySslFieldProvider
     */
    public function testInstantSslRemainsEnabled(array|null $sslField): void
    {
        $resource = $this->createTemporaryResource();
        $resource->setInstantSsl(true);

        $ssl = $this->enableResourceSsl($resource);
        $this->addResourceSslFile($resource, $ssl, 1, new DateTimeImmutable(), ['foo.bar'], SslFile::TYPE_LETSENCRYPT);

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [ResourceEditInfo::FIELD_INSTANT_SSL => (int) true],
                    ),
                    ResourceEditSchema::FIELD_SSL => $sslField,
                ],
            ),
        );
        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $updatedResource = $this->getEntityManager()->find(CdnResource::class, $resource->getId());
        assert($updatedResource instanceof CdnResource);
        self::assertTrue($updatedResource->hasInstantSsl());
        self::assertSslExists($resource, $ssl);
        self::assertUpdatingMessageInQueue($resource->getId());
        self::assertNoCertificateRequestExists($resource);
    }

    /**
     * @param array<string, string>|null $sslField
     *
     * @dataProvider emptySslFieldProvider
     */
    public function testResourceEditWhenInstantSslIsDisabledWithSslCertificateAndKeySetToNull(
        array|null $sslField,
    ): void {
        $resource = $this->createTemporaryResource();

        FlushAndClear::do($this->getEntityManager());

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [ResourceEditInfo::FIELD_INSTANT_SSL => 0],
                    ),
                    ResourceEditSchema::FIELD_SSL => $sslField,
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $dbResource = $this->getEntityManager()->find(CdnResource::class, $resource->getId());
        assert($dbResource instanceof CdnResource);
        self::assertFalse($dbResource->hasInstantSsl());
        self::assertSslDoesNotExist($resource);
        self::assertNoCertificateRequestExists($resource);
    }

    public function testResourceEditFailWhenInstantSslSettingsAreMissingForSslSetup(): void
    {
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $certificatePairGenerator = new CertificatePairGenerator();
        $certificatePair = $certificatePairGenerator->generateRandomCertificatePair();

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData($resource, []),
                    ResourceEditSchema::FIELD_SSL => [
                        ResourceSslSchema::FIELD_KEY => $certificatePair->getPrivateKey(),
                        ResourceSslSchema::FIELD_CERTIFICATE => $certificatePair->getCertificate(),
                    ],
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertNoCertificateRequestExists($resource);
    }

    public function testResourceEditFailWhenInstantSslIsEnabledWithSslCertificateSetup(): void
    {
        $resource = $this->createTemporaryResource();
        FlushAndClear::do($this->getEntityManager());

        $certificatePairGenerator = new CertificatePairGenerator();
        $certificatePair = $certificatePairGenerator->generateRandomCertificatePair();

        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        [ResourceEditInfo::FIELD_INSTANT_SSL => 1],
                    ),
                    ResourceEditSchema::FIELD_SSL => [
                        ResourceSslSchema::FIELD_KEY => $certificatePair->getPrivateKey(),
                        ResourceSslSchema::FIELD_CERTIFICATE => $certificatePair->getCertificate(),
                    ],
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertNoCertificateRequestExists($resource);
    }

    private function getSslFilesCount(CdnResource $resource): int
    {
        return (int) $this->getEntityManager()
            ->createQueryBuilder()
            ->from(SslFile::class, 'f')
            ->select('COUNT(f)')
            ->where('f.ssl = :resource')
            ->setParameter('resource', $resource->getId())
            ->getQuery()
            ->getSingleScalarResult();
    }

    private function assertNoCertificateRequestExists(CdnResource $resource): void
    {
        $letsEncryptRequests = $this->findPendingCertificateRequestsForUnmanagedResource($resource);
        self::assertSame(0, count($letsEncryptRequests));
    }

    private function assertOneCertificateRequestExists(CdnResource $resource): void
    {
        self::assertOneCertificateRequestExistsWithDomains(
            $resource,
            array_merge([$resource->getCdnUrl()], $resource->getCnames()),
        );
    }

    /** @param string[] $expectedDomains */
    private function assertOneCertificateRequestExistsWithDomains(CdnResource $resource, array $expectedDomains): void
    {
        $letsEncryptRequests = $this->findPendingCertificateRequestsForUnmanagedResource($resource);
        self::assertSame(1, count($letsEncryptRequests));

        $request = $letsEncryptRequests[0];
        $actualDomains = $request->getDomains();

        sort($actualDomains);
        sort($expectedDomains);

        self::assertSame($expectedDomains, $actualDomains);

        $tasks = $this->getEntityManager()->getRepository(Task::class)->findBy(['request' => $request]);
        self::assertCount(1, $tasks);
    }

    private function assertSslDoesNotExist(CdnResource $resource): void
    {
        $ssl = $this->getEntityManager()->getRepository(Ssl::class)->find($resource->getId());
        self::assertNull($ssl, 'Certificate should not exist');
    }

    private function assertSslExists(CdnResource $resource, Ssl|null $expectedSsl = null): void
    {
        $ssl = $this->getEntityManager()->getRepository(Ssl::class)->find($resource->getId());
        self::assertNotNull($ssl, 'Certificate should exist');

        if ($expectedSsl === null) {
            return;
        }

        self::assertSame($expectedSsl->getResource()->getId(), $ssl->getResource()->getId());
        self::assertSame($expectedSsl->getAssignedIndex(), $ssl->getAssignedIndex());
        self::assertSame($expectedSsl->getAssignedAt()->format('c'), $ssl->getAssignedAt()->format('c'));
    }

    /** @param string[] $cnames */
    private function makeRequestWithEditingCnames(CdnResource $resource, array $cnames): Response
    {
        $this->client->request(
            Request::METHOD_PATCH,
            sprintf('/resource/%d', $resource->getId()),
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    ResourceEditSchema::FIELD_CDN_RESOURCE => $this->addRequiredData(
                        $resource,
                        ['cnames' => $cnames],
                    ),
                ],
            ),
        );

        return $this->client->getResponse();
    }

    /** @return LetsEncryptRequest[] */
    private function findPendingCertificateRequestsForUnmanagedResource(CdnResource $resource): array
    {
        return $this->getEntityManager()->createQueryBuilder()
            ->from(LetsEncryptRequest::class, 'r')
            ->select('r')
            ->where('r.resource = :resourceId')
            ->setParameter('resourceId', $resource->getId())
            ->andWhere('r.state = :pending')
            ->setParameter('pending', RequestState::PENDING)
            ->getQuery()
            ->getResult();
    }
}
