<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\OriginHeaders;

use Cdn77\NxgApi\Core\Application\Utility\ValueReplacer;
use Cdn77\NxgApi\Resource\Application\Payload\Origin\OriginSchema;
use Cdn77\NxgApi\Resource\Application\Payload\ResourceAddSchema;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\EntityGetter;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller\Origins\EvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\ResponseDecoded;
use Cdn77\NxgApi\Tests\Functional\ResponseEvaluateHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Generator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function Safe\json_encode;

class AddTest extends WebTestCase
{
    use EntityGetter;
    use EvaluateHelper;
    use NotifyResourceChangeHelper;
    use TemporaryData;
    use ResponseEvaluateHelper;

    public const ERROR_STRICTLY_ARRAY
        = 'This value have to be strictly an array (neither 1, 0, null, string etc. is allowed).';

    /** @return Generator<string, array<array<string, string>>> */
    public static function providerOk(): Generator
    {
        yield 'empty' => [[]];
        yield 'single item' => [['one' => 'two']];
        yield 'double item' => [['one' => 'two', 'three' => 'four']];
        yield 'header value with tab (allowed)' => [['HeaderName' => "Value\tWithTab"]];
        yield 'valid RFC 7230 token characters in name' => [['Valid-Header_Name123' => 'value']];
        yield 'valid visible ASCII in value' => [['HeaderName' => 'Value with spaces and symbols: !@#$%^&*()']];
    }

    /** @return Generator<string, array<array<string, string>>> */
    public static function providerOkMultipleOrigins(): Generator
    {
        $array = ['one' => 'two', 'three' => 'four'];

        yield 'default both' => [[], []];
        yield 'default 1, array 2' => [[], $array];
        yield 'array 1, default 2' => [$array, []];
        yield 'array 1, array 2' => [$array, $array];
    }

    /** @return Generator<string, array<int, mixed>> */
    public static function providerFail(): Generator
    {
        yield 'bad type 0' => ['', self::ERROR_STRICTLY_ARRAY];
        yield 'bad type 1' => ['0', self::ERROR_STRICTLY_ARRAY];
        yield 'bad type 2' => ['1', self::ERROR_STRICTLY_ARRAY];
        yield 'bad type 3' => [666, self::ERROR_STRICTLY_ARRAY];
        yield 'bad type 4' => [null, self::ERROR_STRICTLY_ARRAY];
        yield 'bad type 5' => [true, self::ERROR_STRICTLY_ARRAY];
        yield 'bad type 6' => [false, self::ERROR_STRICTLY_ARRAY];

        yield 'Forbidden header name Accept' => [
            ['Accept' => 'Value'],
            'Origin header name "Accept" is forbidden.',
        ];

        yield 'Invalid name with colon' => [
            ['Name:' => 'Value'],
            'Origin header name "Name:" contains invalid characters.',
        ];

        yield 'Invalid value with backslash' => [
            ['Key' => 'value_with_backslash\\'],
            'Origin header value "value_with_backslash\\" contains invalid characters.',
        ];

        yield 'Over limit' => [
            ['Key' => str_repeat('a', 10000)],
            'Maximum size of all origin headers is 8192 bytes.',
        ];

        yield 'Over limit header' => [
            [str_repeat('a', 1100) => 'Value'],
            'Maximum size of origin header (name + value) is 1024 bytes.',
        ];

        // Test cases for newlines, tabs, and other control characters
        yield 'Header name with newline (LF)' => [
            ["Header\nName" => 'Value'],
            'Origin header name "Header' . "\n" . 'Name" contains invalid characters.',
        ];

        yield 'Header name with carriage return (CR)' => [
            ["Header\rName" => 'Value'],
            'Origin header name "Header' . "\r" . 'Name" contains invalid characters.',
        ];

        yield 'Header name with CRLF' => [
            ["Header\r\nName" => 'Value'],
            'Origin header name "Header' . "\r\n" . 'Name" contains invalid characters.',
        ];

        yield 'Header name with tab' => [
            ["Header\tName" => 'Value'],
            'Origin header name "Header' . "\t" . 'Name" contains invalid characters.',
        ];

        yield 'Header value with newline (LF)' => [
            ['HeaderName' => "Value\nWithNewline"],
            'Origin header value "Value' . "\n" . 'WithNewline" contains invalid characters.',
        ];

        yield 'Header value with carriage return (CR)' => [
            ['HeaderName' => "Value\rWithCR"],
            'Origin header value "Value' . "\r" . 'WithCR" contains invalid characters.',
        ];

        yield 'Header value with CRLF' => [
            ['HeaderName' => "Value\r\nWithCRLF"],
            'Origin header value "Value' . "\r\n" . 'WithCRLF" contains invalid characters.',
        ];

        yield 'Header value with tab' => [
            ['HeaderName' => "Value\tWithTab"],
            'Origin header value "Value' . "\t" . 'WithTab" contains invalid characters.',
        ];

        yield 'Header name with vertical tab' => [
            ["Header\vName" => 'Value'],
            'Origin header name "Header' . "\v" . 'Name" contains invalid characters.',
        ];

        yield 'Header value with vertical tab' => [
            ['HeaderName' => "Value\vWithVTab"],
            'Origin header value "Value' . "\v" . 'WithVTab" contains invalid characters.',
        ];

        yield 'Header name with form feed' => [
            ["Header\fName" => 'Value'],
            'Origin header name "Header' . "\f" . 'Name" contains invalid characters.',
        ];

        yield 'Header value with form feed' => [
            ['HeaderName' => "Value\fWithFF"],
            'Origin header value "Value' . "\f" . 'WithFF" contains invalid characters.',
        ];

        yield 'Header name with null byte' => [
            ["Header\0Name" => 'Value'],
            'Origin header name "Header' . "\0" . 'Name" contains invalid characters.',
        ];

        yield 'Header value with null byte' => [
            ['HeaderName' => "Value\0WithNull"],
            'Origin header value "Value' . "\0" . 'WithNull" contains invalid characters.',
        ];

        yield 'Header name with bell character' => [
            ["Header\aName" => 'Value'],
            'Origin header name "Header' . "\a" . 'Name" contains invalid characters.',
        ];

        yield 'Header value with bell character' => [
            ['HeaderName' => "Value\aWithBell"],
            'Origin header value "Value' . "\a" . 'WithBell" contains invalid characters.',
        ];

        yield 'Header name with backspace' => [
            ["Header\bName" => 'Value'],
            'Origin header name "Header' . "\b" . 'Name" contains invalid characters.',
        ];

        yield 'Header value with backspace' => [
            ['HeaderName' => "Value\bWithBackspace"],
            'Origin header value "Value' . "\b" . 'WithBackspace" contains invalid characters.',
        ];

        yield 'Header name with escape character' => [
            ["Header\eName" => 'Value'],
            'Origin header name "Header' . "\e" . 'Name" contains invalid characters.',
        ];

        yield 'Header value with escape character' => [
            ['HeaderName' => "Value\eWithEscape"],
            'Origin header value "Value' . "\e" . 'WithEscape" contains invalid characters.',
        ];

        // Test Unicode control characters
        yield 'Header name with Unicode line separator' => [
            ["Header\u{2028}Name" => 'Value'],
            'Origin header name "Header' . "\u{2028}" . 'Name" contains invalid characters.',
        ];

        yield 'Header value with Unicode line separator' => [
            ['HeaderName' => "Value\u{2028}WithLineSep"],
            'Origin header value "Value' . "\u{2028}" . 'WithLineSep" contains invalid characters.',
        ];

        yield 'Header name with Unicode paragraph separator' => [
            ["Header\u{2029}Name" => 'Value'],
            'Origin header name "Header' . "\u{2029}" . 'Name" contains invalid characters.',
        ];

        yield 'Header value with Unicode paragraph separator' => [
            ['HeaderName' => "Value\u{2029}WithParaSep"],
            'Origin header value "Value' . "\u{2029}" . 'WithParaSep" contains invalid characters.',
        ];

        // Test multiple control characters in one header
        yield 'Header name with multiple control chars' => [
            ["Header\n\t\rName" => 'Value'],
            'Origin header name "Header' . "\n\t\r" . 'Name" contains invalid characters.',
        ];

        yield 'Header value with multiple control chars' => [
            ['HeaderName' => "Value\n\t\rWithMultiple"],
            'Origin header value "Value' . "\n\t\r" . 'WithMultiple" contains invalid characters.',
        ];

        // Test edge cases with whitespace
        yield 'Header name with leading space' => [
            [' HeaderName' => 'Value'],
            'Origin header name " HeaderName" contains invalid characters.',
        ];

        yield 'Header name with trailing space' => [
            ['HeaderName ' => 'Value'],
            'Origin header name "HeaderName " contains invalid characters.',
        ];

        yield 'Header value with leading space' => [
            ['HeaderName' => ' Value'],
            'Origin header value " Value" contains invalid characters.',
        ];

        yield 'Header value with trailing space' => [
            ['HeaderName' => 'Value '],
            'Origin header value "Value " contains invalid characters.',
        ];

        // Note: Control character tests are handled in unit tests due to JSON encoding limitations
        // These functional tests focus on cases that can be properly JSON encoded


















            'Origin header value "Value' . "\x7F" . 'WithDEL" contains invalid characters.',
        ];

        // Test edge case: Tab character (0x09) should be allowed in values but not in names
        yield 'Header name with TAB (0x09) - should fail' => [
            ["Header\x09Name" => 'Value'],
            'Origin header name "Header' . "\x09" . 'Name" contains invalid characters.',
        ];

        // Note: Tab in header values should be allowed according to RFC 7230
        // This test case would pass validation, so it's not included in providerFail

        // Test combinations of control characters
        yield 'Header name with multiple control chars (NUL+LF+CR)' => [
            ["Header\x00\x0A\x0DName" => 'Value'],
            'Origin header name "Header' . "\x00\x0A\x0D" . 'Name" contains invalid characters.',
        ];

        yield 'Header value with multiple control chars (NUL+LF+CR)' => [
            ['HeaderName' => "Value\x00\x0A\x0DWithMultiple"],
            'Origin header value "Value' . "\x00\x0A\x0D" . 'WithMultiple" contains invalid characters.',
        ];

    }

    /** @return Generator<string, array<int, array<mixed>|int|bool|string|null>> */
    public static function providerFailMultipleOrigins(): Generator
    {
        yield 'bad type 1 - first ok, second bad' => [
            [],
            '0',
            [self::ERROR_STRICTLY_ARRAY],
            ['origins.origins[1].origin_headers' => [self::ERROR_STRICTLY_ARRAY]],
        ];

        yield 'bad type 1 - first bad, second ok' => [
            true,
            [],
            [self::ERROR_STRICTLY_ARRAY],
            ['origins.origins[0].origin_headers' => [self::ERROR_STRICTLY_ARRAY]],
        ];

        yield 'bad type 1 - both bad' => [
            '0',
            1,
            [self::ERROR_STRICTLY_ARRAY, self::ERROR_STRICTLY_ARRAY],
            [
                'origins.origins[0].origin_headers' => [self::ERROR_STRICTLY_ARRAY],
                'origins.origins[1].origin_headers' => [self::ERROR_STRICTLY_ARRAY],
            ],
        ];

        yield 'Forbidden header names Accept' => [
            ['acCept' => 'Value'],
            ['ACCEPT' => 'Value'],
            ['Origin header name "acCept" is forbidden.', 'Origin header name "ACCEPT" is forbidden.'],
            [
                'origins.origins[0].origin_headers' => ['Origin header name "acCept" is forbidden.'],
                'origins.origins[1].origin_headers' => ['Origin header name "ACCEPT" is forbidden.'],
            ],
        ];

        yield 'Forbidden header names via and range' => [
            ['via' => 'Value'],
            ['range' => 'Value'],
            ['Origin header name "via" is forbidden.', 'Origin header name "range" is forbidden.'],
            [
                'origins.origins[0].origin_headers' => ['Origin header name "via" is forbidden.'],
                'origins.origins[1].origin_headers' => ['Origin header name "range" is forbidden.'],
            ],
        ];

        yield 'Invalid header names "\\" and "{"' => [
            ['\\' => 'Value'],
            ['{' => 'Value'],
            [
                'Origin header name "\\" contains invalid characters.',
                'Origin header name "{" contains invalid characters.',
            ],
            [
                'origins.origins[0].origin_headers' => ['Origin header name "\\" contains invalid characters.'],
                'origins.origins[1].origin_headers' => ['Origin header name "{" contains invalid characters.'],
            ],
        ];
    }

    /**
     * @param array<string, string> $originHeaders
     *
     * @dataProvider providerOk
     */
    public function testOk(array $originHeaders): void
    {
        $originData = $this->prepareRequestData($originHeaders);
        $response = $this->callApiGetResponse([$originData]);

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        self::assertIsInt($resourceId);
        self::assertCreatingMessageInQueue($resourceId);

        $resource = $this->getResource($resourceId);

        if ($originHeaders !== []) {
            self::assertSame(
                $originHeaders,
                $response->decodedContent['cdn_resource']['origin_headers'],
            );
        }

        self::assertSame(
            ValueReplacer::emptyArrayToNull($originHeaders),
            $resource->getMainOrigin()->getOriginHeaders(),
        );
    }

    /**
     * @param array<string, string> $originHeadersA
     * @param array<string, string> $originHeadersB
     *
     * @dataProvider providerOkMultipleOrigins
     */
    public function testOkMultipleOrigins(array $originHeadersA, array $originHeadersB): void
    {
        $originDataA = $this->prepareRequestData($originHeadersA);
        $originDataB = $this->prepareRequestData($originHeadersB, 2);
        $response = $this->callApiGetResponse([$originDataA, $originDataB]);

        self::assertSame(Response::HTTP_OK, $response->statusCode);
        $resourceId = $response->decodedContent['cdn_resource']['cdn_reference'];

        self::assertIsInt($resourceId);
        self::assertCreatingMessageInQueue($resourceId);

        $resource = $this->getResource($resourceId);

        if ($originHeadersA !== []) {
            self::assertSame(
                $originHeadersA,
                $response->decodedContent['cdn_resource']['origin_headers'],
            );
        }

        self::assertSame(
            ValueReplacer::emptyArrayToNull($originHeadersA),
            $resource->getMainOrigin()->getOriginHeaders(),
        );
        self::assertSame(
            ValueReplacer::emptyArrayToNull($originHeadersB),
            $resource->getSecondOrigin()->getOriginHeaders(),
        );
    }

    /**
     * @param mixed $originHeaders
     *
     * @dataProvider providerFail
     */
    public function testFail($originHeaders, string $errorMsg): void
    {
        $originData = $this->prepareRequestData($originHeaders);
        $response = $this->callApiGetResponse([$originData]);

        $this->evaluateUnprocessableEntityResponse($response, $errorMsg, 'origins[0].origin_headers', 'origins');
    }

    /**
     * @param mixed $originHeadersA
     * @param mixed $originHeadersB
     * @param array<string> $errors
     * @param array<string, array<string>> $fields
     *
     * @dataProvider providerFailMultipleOrigins
     */
    public function testFailMultipleOrigins($originHeadersA, $originHeadersB, array $errors, array $fields): void
    {
        $originDataA = $this->prepareRequestData($originHeadersA);
        $originDataB = $this->prepareRequestData($originHeadersB, 2);
        $response = $this->callApiGetResponse([$originDataA, $originDataB]);

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->statusCode);
        self::assertSame($errors, $response->decodedContent['errors']);
        self::assertSame($fields, $response->decodedContent['fields']);
    }

    public function testFailWhenOriginHeadersMissing(): void
    {
        $originData = $this->prepareDataForOrigin();
        unset($originData['origin_headers']);

        $response = $this->callApiGetResponse([$originData]);

        self::evaluateUnprocessableEntityResponse(
            $response,
            'This value have to be strictly an array (neither 1, 0, null, string etc. is allowed).',
            'origins[0].origin_headers',
            'origins',
        );
    }

    public function testFailWhenOriginHeadersMissingMultipleOrigins(): void
    {
        $originDataA = $this->prepareDataForOrigin();
        $originDataB = $this->prepareDataForOrigin(2);
        unset($originDataB['origin_headers']);

        $response = $this->callApiGetResponse([$originDataA, $originDataB]);

        self::evaluateUnprocessableEntityResponse(
            $response,
            self::ERROR_STRICTLY_ARRAY,
            'origins[1].origin_headers',
            'origins',
        );
    }

    /**
     * @param mixed $originHeaders
     *
     * @return array<string, mixed>
     */
    private function prepareRequestData($originHeaders, int $priority = 1): array
    {
        $dataForOrigin = $this->prepareDataForOrigin($priority);

        $dataForOrigin[OriginSchema::FIELD_ORIGIN_HEADERS] = $originHeaders;

        return $dataForOrigin;
    }

    /** @param array<int, array<string, mixed>> $origins */
    private function callApiGetResponse(array $origins): ResponseDecoded
    {
        $this->client->request(
            Request::METHOD_POST,
            '/cdn_resources.json',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode([
                'cdn_resource' => $this->prepareDataForNewCdn(),
                ResourceAddSchema::FIELD_ORIGINS => $origins,
            ]),
        );

        return ResponseDecoded::fromResponse($this->client->getResponse());
    }
}
