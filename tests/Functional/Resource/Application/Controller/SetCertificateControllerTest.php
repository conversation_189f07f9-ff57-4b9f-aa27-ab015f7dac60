<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Functional\Resource\Application\Controller;

use Cdn77\NxgApi\Entity\Legacy\CdnResource;
use Cdn77\NxgApi\Entity\Legacy\Ssl;
use Cdn77\NxgApi\Entity\Legacy\SslFile;
use Cdn77\NxgApi\Entity\LetsEncrypt\CertificatePair;
use Cdn77\NxgApi\Resource\Application\Payload\SetCertificateSchema;
use Cdn77\NxgApi\Service\ExternalApi\CertificateBucket\CertificateBucket;
use Cdn77\NxgApi\Tests\Functional\Controller\Legacy\TemporaryData;
use Cdn77\NxgApi\Tests\Functional\NotifyResourceChangeHelper;
use Cdn77\NxgApi\Tests\Functional\WebTestCase;
use Cdn77\NxgApi\Tests\Generators\CertificateDefinitions;
use Cdn77\NxgApi\Tests\Generators\CertificatePairGenerator;
use Cdn77\TestUtils\Stub;
use DateTimeImmutable;
use Generator;
use ReflectionProperty;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

use function array_keys;
use function array_map;
use function assert;
use function Safe\json_decode;
use function Safe\json_encode;

final class SetCertificateControllerTest extends WebTestCase
{
    use NotifyResourceChangeHelper;
    use TemporaryData;
    use CertificateDefinitions;

    public function testWithNoPreviousCertificate(): void
    {
        $now = new DateTimeImmutable();
        $resourceIds = array_map(
            static fn (CdnResource $resource): int => $resource->getId(),
            $this->createTemporaryResources(),
        );

        $certificatePair = (new CertificatePairGenerator())->generateRandomCertificatePair();

        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_POST,
            '/resources/set-certificate',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    SetCertificateSchema::FIELD_RESOURCE_IDS => $resourceIds,
                    SetCertificateSchema::FIELD_CERTIFICATE => $certificatePair->getCertificate(),
                    SetCertificateSchema::FIELD_PRIVATE_KEY => $certificatePair->getPrivateKey(),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $sslRepository = $this->getEntityManager()->getRepository(Ssl::class);
        foreach ($resourceIds as $resourceId) {
            $ssl = $sslRepository->find($resourceId);
            self::assertInstanceOf(Ssl::class, $ssl);
            self::assertNotNull($ssl);

            self::assertSame(1, $ssl->getFiles()->count());

            $file = $ssl->getFiles()->first();
            self::assertInstanceOf(SslFile::class, $file);
            self::assertSame(SslFile::TYPE_CUSTOM, $file->getType());
            self::assertSame($ssl->getAssignedIndex(), $file->getIndex());
            self::assertSame($ssl->getAssignedIndex(), 1);
            self::assertGreaterThanOrEqual($now->getTimestamp(), $ssl->getAssignedAt()->getTimestamp());
            self::assertSame(
                $file->getExpiresAt()->getTimestamp(),
                $this->extractCertificateExpiration($certificatePair)->getTimestamp(),
            );
            self::assertSame([], $this->extractCertificateDomains($certificatePair));
        }
    }

    public function testSslFileAlreadyExists(): void
    {
        $now = new DateTimeImmutable();
        $resources = $this->createTemporaryResources();
        $resourceIds = array_keys($resources);

        $resource = Stub::create(CdnResource::class, ['id' => $resourceIds[0]]);
        $ssl = Stub::create(Ssl::class, ['resource' => $resource]);
        $sslFile = Stub::create(SslFile::class, ['ssl' => $ssl, 'index' => 1]);
        $bucket = self::$container->get(CertificateBucket::class);
        assert($bucket instanceof CertificateBucket);
        $bucket->save($sslFile, new CertificatePair('CERTIFICATE', 'PRIVATE_KEY'));

        $certificatePair = (new CertificatePairGenerator())->generateRandomCertificatePair();

        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_POST,
            '/resources/set-certificate',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    SetCertificateSchema::FIELD_RESOURCE_IDS => $resourceIds,
                    SetCertificateSchema::FIELD_CERTIFICATE => $certificatePair->getCertificate(),
                    SetCertificateSchema::FIELD_PRIVATE_KEY => $certificatePair->getPrivateKey(),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $sslRepository = $this->getEntityManager()->getRepository(Ssl::class);
        foreach ($resourceIds as $resourceId) {
            $ssl = $sslRepository->find($resourceId);
            self::assertInstanceOf(Ssl::class, $ssl);
            self::assertNotNull($ssl);

            self::assertSame(1, $ssl->getFiles()->count());

            $file = $ssl->getFiles()->first();
            self::assertInstanceOf(SslFile::class, $file);
            self::assertSame(SslFile::TYPE_CUSTOM, $file->getType());
            self::assertSame($ssl->getAssignedIndex(), $file->getIndex());
            self::assertSame($ssl->getAssignedIndex(), 1);
            self::assertGreaterThanOrEqual($now->getTimestamp(), $ssl->getAssignedAt()->getTimestamp());

            self::assertSame(
                $file->getExpiresAt()->getTimestamp(),
                $this->extractCertificateExpiration($certificatePair)->getTimestamp(),
            );
            self::assertSame([], $this->extractCertificateDomains($certificatePair));
        }

        $replacedSslCertificateFile = $bucket->get($sslFile);
        self::assertEquals($certificatePair->getCertificate(), $replacedSslCertificateFile->getCertificate());
        self::assertEquals($certificatePair->getPrivateKey(), $replacedSslCertificateFile->getPrivateKey());
    }

    public function testWithExistingPreviousCertificateFiles(): void
    {
        $now = new DateTimeImmutable();
        $resourceIds = [];
        $resources = $this->createTemporaryResources();
        $certificatePair = (new CertificatePairGenerator())->generateRandomCertificatePair();
        foreach ($resources as $resource) {
            $resourceIds[] = $resource->getId();
            $resource->setInstantSsl(true);

            $ssl = $this->enableResourceSsl($resource);

            for ($i = 1; $i <= 3; $i++) {
                $sslFile = $this->addResourceSslFile(
                    $resource,
                    $ssl,
                    $i,
                    new DateTimeImmutable(),
                    ['foo.bar'],
                    SslFile::TYPE_LETSENCRYPT,
                );

                $rp = new ReflectionProperty(SslFile::class, 'createdAt');
                $rp->setAccessible(true);
                $rp->setValue($sslFile, new DateTimeImmutable('- ' . ((3 - $i) * 10) . ' minutes'));
            }

            $ssl->setAssignedIndex(2);
            $ssl->setAssignedAt(new DateTimeImmutable('- 15 minutes'));
        }

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_POST,
            '/resources/set-certificate',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    SetCertificateSchema::FIELD_RESOURCE_IDS => $resourceIds,
                    SetCertificateSchema::FIELD_CERTIFICATE => $certificatePair->getCertificate(),
                    SetCertificateSchema::FIELD_PRIVATE_KEY => $certificatePair->getPrivateKey(),
                ],
            ),
        );

        self::assertSame(Response::HTTP_NO_CONTENT, $this->client->getResponse()->getStatusCode());

        $sslRepository = $this->getEntityManager()->getRepository(Ssl::class);
        foreach ($resources as $resource) {
            $ssl = $sslRepository->find($resource->getId());
            self::assertInstanceOf(Ssl::class, $ssl);
            self::assertNotNull($ssl);

            self::assertSame(4, $ssl->getFiles()->count());

            $file = $ssl->getFiles()->get(4);
            self::assertInstanceOf(SslFile::class, $file);
            self::assertSame(SslFile::TYPE_CUSTOM, $file->getType());
            self::assertSame($ssl->getAssignedIndex(), $file->getIndex());
            self::assertSame($ssl->getAssignedIndex(), 4);
            self::assertGreaterThanOrEqual($now->getTimestamp(), $ssl->getAssignedAt()->getTimestamp());

            $updatedResource = $this->getEntityManager()->getRepository(CdnResource::class)->find($resource->getId());
            self::assertFalse($updatedResource->hasInstantSsl());
        }
    }

    public function testWithInvalidCertificatePair(): void
    {
        $resourceIds = array_map(
            static fn (CdnResource $resource): int => $resource->getId(),
            $this->createTemporaryResources(),
        );

        $certificatePair = (new CertificatePairGenerator())->generateRandomInvalidCertificatePair();

        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_POST,
            '/resources/set-certificate',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    SetCertificateSchema::FIELD_RESOURCE_IDS => $resourceIds,
                    SetCertificateSchema::FIELD_CERTIFICATE => $certificatePair->getCertificate(),
                    SetCertificateSchema::FIELD_PRIVATE_KEY => $certificatePair->getPrivateKey(),
                ],
            ),
        );

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $this->client->getResponse()->getStatusCode());
        self::assertNoMessageInQueue();
    }

    public function testWithInvalidCertificateChain(): void
    {
        $resourceIds = array_map(
            static fn (CdnResource $resource): int => $resource->getId(),
            $this->createTemporaryResources(),
        );

        $certificatePairGenerator = new CertificatePairGenerator();
        $certificatePair = $certificatePairGenerator->generateRandomCertificatePair();

        $certificatePairChain = new CertificatePair(
            $certificatePair->getCertificate()
            . $certificatePairGenerator->generateRandomCertificate()
            . "-----BEGIN CERTIFICATE-----\ninvalidCertificate\n-----END CERTIFICATE-----\n"
            . $certificatePairGenerator->generateRandomCertificate(),
            $certificatePair->getPrivateKey(),
        );

        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_POST,
            '/resources/set-certificate',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    SetCertificateSchema::FIELD_RESOURCE_IDS => $resourceIds,
                    SetCertificateSchema::FIELD_CERTIFICATE => $certificatePairChain->getCertificate(),
                    SetCertificateSchema::FIELD_PRIVATE_KEY => $certificatePairChain->getPrivateKey(),
                ],
            ),
        );

        $response = $this->client->getResponse();

        self::assertSame(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        self::assertSame(
            [
                'errors' => ['The certificate chain is malformed, probably 2. intermediate is corrupted.'],
            ],
            json_decode($response->getContent(), true),
        );
        self::assertNoMessageInQueue();
    }

    /**
     * @param string[] $domains
     *
     * @dataProvider certificatesProvider
     */
    public function testWithMultipleCertificateConfigurations(
        CertificatePair $certificatePair,
        array $domains,
        DateTimeImmutable $expiration,
    ): void {
        $now = new DateTimeImmutable();
        $resourceIds = array_map(
            static fn (CdnResource $resource): int => $resource->getId(),
            $this->createTemporaryResources(),
        );

        $this->getEntityManager()->clear();

        $this->client->request(
            Request::METHOD_POST,
            '/resources/set-certificate',
            [],
            [],
            static::getDefaultHeaders(),
            json_encode(
                [
                    SetCertificateSchema::FIELD_RESOURCE_IDS => $resourceIds,
                    SetCertificateSchema::FIELD_CERTIFICATE => $certificatePair->getCertificate(),
                    SetCertificateSchema::FIELD_PRIVATE_KEY => $certificatePair->getPrivateKey(),
                ],
            ),
        );

        self::assertSame(Response::HTTP_NO_CONTENT, $this->client->getResponse()->getStatusCode());

        foreach ($resourceIds as $resourceId) {
            $ssl = $this->getEntityManager()->getRepository(Ssl::class)->find($resourceId);
            self::assertInstanceOf(Ssl::class, $ssl);
            self::assertNotNull($ssl);

            self::assertSame(1, $ssl->getFiles()->count());

            $file = $ssl->getFiles()->first();
            self::assertInstanceOf(SslFile::class, $file);
            self::assertSame(SslFile::TYPE_CUSTOM, $file->getType());
            self::assertSame($ssl->getAssignedIndex(), $file->getIndex());
            self::assertSame($ssl->getAssignedIndex(), 1);
            self::assertGreaterThanOrEqual($now->getTimestamp(), $ssl->getAssignedAt()->getTimestamp());

            self::assertSame(
                $expiration->getTimestamp(),
                $this->extractCertificateExpiration($certificatePair)->getTimestamp(),
            );
            self::assertSame($file->getExpiresAt()->getTimestamp(), $expiration->getTimestamp());

            self::assertSame($domains, $this->extractCertificateDomains($certificatePair));
            self::assertSame($domains, $file->getDomains());
        }
    }

    /** @return Generator<list<CertificatePair|array|DateTimeImmutable>> */
    public function certificatesProvider(): iterable
    {
        yield $this->getCertificateWithCommonNameAlternativeNames();
        yield $this->getCertificateWithCommonNameAndOneDifferentAlternativeName();
        yield $this->getCertificateWithCommonNameAndMultipleDifferentAternativeNames();
        yield $this->getCertificateWithCommonNameAndMatchingAlternativeNames();
        yield $this->getCertificateWithCommonNameAndMultipleAndNonDnsAlternativeNames();
    }
}
