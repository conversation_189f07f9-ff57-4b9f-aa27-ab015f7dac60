<?php

declare(strict_types=1);

namespace Cdn77\NxgApi\Tests\Unit\Validator\Constraints\Resource;

use Cdn77\NxgApi\Validator\Constraints\Resource\OriginHeaders;
use Cdn77\NxgApi\Validator\Constraints\Resource\OriginHeadersValidator;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Symfony\Component\Validator\Violation\ConstraintViolationBuilderInterface;

class OriginHeadersValidatorTest extends TestCase
{
    private OriginHeadersValidator $validator;
    private ExecutionContextInterface $context;
    private ConstraintViolationBuilderInterface $violationBuilder;
    private OriginHeaders $constraint;

    protected function setUp(): void
    {
        $this->validator = new OriginHeadersValidator();
        $this->context = $this->createMock(ExecutionContextInterface::class);
        $this->violationBuilder = $this->createMock(ConstraintViolationBuilderInterface::class);
        $this->constraint = new OriginHeaders();

        $this->validator->initialize($this->context);
    }

    /**
     * @dataProvider providerValidHeaders
     */
    public function testValidHeaders(array $headers): void
    {
        $this->context->expects($this->never())
            ->method('buildViolation');

        $this->validator->validate($headers, $this->constraint);
    }

    /**
     * @dataProvider providerInvalidHeaders
     */
    public function testInvalidHeaders(array $headers, string $expectedMessage): void
    {
        $this->violationBuilder->expects($this->once())
            ->method('setParameter')
            ->willReturnSelf();

        $this->violationBuilder->expects($this->once())
            ->method('addViolation');

        $this->context->expects($this->once())
            ->method('buildViolation')
            ->with($expectedMessage)
            ->willReturn($this->violationBuilder);

        $this->validator->validate($headers, $this->constraint);
    }

    public static function providerValidHeaders(): \Generator
    {
        yield 'empty headers' => [[]];
        yield 'simple header' => [['Content-Type' => 'application/json']];
        yield 'header with tab in value' => [['Header' => "Value\tWithTab"]];
        yield 'valid token characters' => [['Valid-Header_Name123' => 'value']];
        yield 'visible ASCII in value' => [['Header' => 'Value with spaces: !@#$%^&*()']];
    }

    public static function providerInvalidHeaders(): \Generator
    {
        // Test forbidden header names
        yield 'forbidden Accept header' => [
            ['Accept' => 'value'],
            'Origin header name "%string%" is forbidden.'
        ];

        yield 'forbidden via header' => [
            ['via' => 'value'],
            'Origin header name "%string%" is forbidden.'
        ];

        // Test ASCII control characters in header names (0-31 and 127)
        yield 'header name with NUL (0x00)' => [
            ["Header\x00Name" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with SOH (0x01)' => [
            ["Header\x01Name" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with STX (0x02)' => [
            ["Header\x02Name" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with ETX (0x03)' => [
            ["Header\x03Name" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with EOT (0x04)' => [
            ["Header\x04Name" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with ENQ (0x05)' => [
            ["Header\x05Name" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with ACK (0x06)' => [
            ["Header\x06Name" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with BEL (0x07)' => [
            ["Header\x07Name" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with BS (0x08)' => [
            ["Header\x08Name" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with TAB (0x09)' => [
            ["Header\x09Name" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with LF (0x0A)' => [
            ["Header\x0AName" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with VT (0x0B)' => [
            ["Header\x0BName" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with FF (0x0C)' => [
            ["Header\x0CName" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with CR (0x0D)' => [
            ["Header\x0DName" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with SO (0x0E)' => [
            ["Header\x0EName" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with SI (0x0F)' => [
            ["Header\x0FName" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with DLE (0x10)' => [
            ["Header\x10Name" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with DC1 (0x11)' => [
            ["Header\x11Name" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with DC2 (0x12)' => [
            ["Header\x12Name" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with DC3 (0x13)' => [
            ["Header\x13Name" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with DC4 (0x14)' => [
            ["Header\x14Name" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with NAK (0x15)' => [
            ["Header\x15Name" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with SYN (0x16)' => [
            ["Header\x16Name" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with ETB (0x17)' => [
            ["Header\x17Name" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with CAN (0x18)' => [
            ["Header\x18Name" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with EM (0x19)' => [
            ["Header\x19Name" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with SUB (0x1A)' => [
            ["Header\x1AName" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with ESC (0x1B)' => [
            ["Header\x1BName" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with FS (0x1C)' => [
            ["Header\x1CName" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with GS (0x1D)' => [
            ["Header\x1DName" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with RS (0x1E)' => [
            ["Header\x1EName" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with US (0x1F)' => [
            ["Header\x1FName" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with DEL (0x7F)' => [
            ["Header\x7FName" => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        // Test ASCII control characters in header values (except tab which is allowed)
        yield 'header value with NUL (0x00)' => [
            ['Header' => "Value\x00WithNUL"],
            'Origin header value "%string%" contains invalid characters.'
        ];

        yield 'header value with LF (0x0A)' => [
            ['Header' => "Value\x0AWithLF"],
            'Origin header value "%string%" contains invalid characters.'
        ];

        yield 'header value with CR (0x0D)' => [
            ['Header' => "Value\x0DWithCR"],
            'Origin header value "%string%" contains invalid characters.'
        ];

        yield 'header value with DEL (0x7F)' => [
            ['Header' => "Value\x7FWithDEL"],
            'Origin header value "%string%" contains invalid characters.'
        ];

        // Test invalid characters in header names
        yield 'header name with colon' => [
            ['Name:' => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        yield 'header name with space' => [
            ['Name With Space' => 'value'],
            'Origin header name "%string%" contains invalid characters.'
        ];

        // Note: Backslash is actually allowed in header values according to RFC 7230
    }
}
